#app {
  taro-view-core {
    // 底部导航
    --tabbar-height: 85px;
    --tabbar-item-font-size: 12px;
    --tabbar-item-icon-size: 20px;
    --tabbar-item-color: #666;
    --tabbar-item-active-color: var(--primary-color);
    --tabbar-item-margin-bottom: 4px;

    // 顶部导航
    --navbar-height: 44px;
    --navbar-title-font-size: 16px;
    --navbar-line-height: 44px;
    --navbar-icon-color: rgba(0, 0, 0, 0.9);
    --navbar-icon-font-size: 20px;

    --search-input-height: 40px;
    --search-padding: 16px;
    --search-field-padding: 8px;
    --search-content-padding-left: 8px;

    --dropdown-menu-height: 40px;
    --dropdown-menu-background-color: #FAFAFA;;
    --dropdown-menu-title-font-size: 14px;
    --dropdown-menu-title-line-height: 40px;

    --tab-active-color: var(--primary-color);
    --tabs-active-color: var(--primary-color);
    --tabs-wrap-height: 40px;
    --tabs-line-height: 2px;
  }

  .taroify-button--small {
    font-size: 14px;
    padding: 8px 16px;
  }

  .taroify-native-input {
    font-size: 15px;
  }

  .taroify-search {
    --cell-font-size: 16px;
    --cell-line-height: 24px;
    --input-line-height: 24px;
    --input-height: 24px;
    --input-clear-icon-size: 16px;
    --form-item-icon-size: 16px;
    .taroify-search__field {
      padding: 8px;
      background-color: transparent;
    }
  }

  .taroify-navbar__title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 下拉弹框
  .taroify-dropdown-menu-item {
    --cell-font-size: 16px;
    --cell-line-height: 24px;
    --input-height: 24px;
    --input-clear-icon-size: 16px;
    --form-item-icon-size: 16px;
  }
  .taroify-dropdown-menu__bar {
    box-shadow: none !important;
  }
  .taroify-dropdown-menu-title--active .taroify-dropdown-menu-title__content {
    color: var(--primary-color);
  }
  .taroify-dropdown-menu-option--active {
    color: var(--primary-color);
  }
  .taroify-dropdown-menu-item__content {
    .taroify-cell {
      padding: 16px 32px;
    }
  }

  // 空
  .taroify-empty {
    padding: 16px 0;
  }
  .taroify-empty__image {
    width: 160px;
    height: 160px;
  }
  .taroify-empty__description {
    width: 100%;
    text-align: center;
    font-size: 14px;
    margin-top: 0;
    padding-top: 0;
  }

  // 时间轴
  .taroify-step--completed {
    .taroify-step__icon {
      color: var(--primary-color);
    }
    .taroify-step__line,
    .taroify-step__circle {
      background-color: var(--primary-color);
    }

  }
  .taroify-step__line{
    background: none;
    left: -0.82rem;
    top: 1.3rem;
    height: 72%;
    border-left: 1px dashed #CCCCCC;
  }

  .taroify-step--active {
    .taroify-step__label,
    .taroify-step__icon {
      color: var(--primary-color);
    }

    .taroify-step__circle {
      background-color: var(--primary-color);
    }
  }
  .taroify-step--vertical{
    padding-bottom: 8px;

    &::after{
      border: none;
    }
  }

  .taroify-dropdown-menu-option--active .taroify-dropdown-menu-option__icon{
    color: var(--primary-color);
  }
}
