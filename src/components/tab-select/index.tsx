import { Flex } from '@taroify/core'
import { View } from '@tarojs/components'
import cls from 'classnames'
import { FC } from 'react'
import './index.scss'

export type TabCompProps = {
  empiId?: string
}

export type Tab = {
  text: string
  key: string
  comp: FC<TabCompProps>
}

type Props = {
  value: Tab
  onChange: (val: Tab) => void
  opened?: boolean
  onOpenedChange?: (val: boolean) => void
  tabs: Tab[]
}

export default function TabSelect({ value, onChange, tabs }: Props) {
  return (
    <View className='styled-tab-select-wrapper'>
      {tabs.map((item) => (
        <Flex.Item onClick={() => onChange(item)} key={item.key} className={cls('tab', { 'active-tab': item.key === value.key })}>
          {item.text}
        </Flex.Item>
      ))}
    </View>
  )
}
