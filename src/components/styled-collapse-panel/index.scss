.collapse-panel {
  background-color: #FAFAFA;
  margin-bottom: 12px;
  border-radius: 8px;
  .collapse-panel-header {
    padding: 12px;
    background: #E8F9F5;
    border-radius: 8px;
    position: relative;
    .anchor {
      width: 16px;
      height: 16px;
      position: absolute;
      top: 12px;
      right: 12px;
      transition: transform 0.3s;
    }
    .anchor-expanded {
      transform: rotate(-180deg);
    }
  }
  .collapse-panel-body {
    transition: height 0.3s;
    width: 100%;
    overflow: hidden;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}
