import { ReactNode, useEffect, useRef, useState, memo, useCallback } from 'react'
import { nextTick } from '@tarojs/taro'
import { View, Image } from '@tarojs/components'
import { getRects } from '@/utils/dom'
import cls from 'classnames'
import arrowBottomSolidUrl from '@/assets/svg/arrow-bottom-solid.svg'
import './index.scss'

type Props = {
  title: ReactNode
  bodyRender: (isExpanded: boolean, refreshContentHeight: () => void) => ReactNode
  async?: boolean
}

function _StyledCollapsePanel({ title, bodyRender, async }: Props) {
  const firstRef = useRef(false)
  const bodyRef = useRef<HTMLDivElement>(null)
  const [isExpanded, setIsExpanded] = useState(false)
  const [height, setHeight] = useState('0')
  const refreshContentHeight = useCallback(() => {
    nextTick(() => {
      const rect = getRects(bodyRef)
      // -2是因为有1px的border
      setHeight(Math.ceil(rect ? rect.height - 2 : 0) + 'px')
    })
  }, [])

  useEffect(() => {
    if (isExpanded) {
      if (async) {
        if (!firstRef.current) {
          firstRef.current = true
        } else {
          refreshContentHeight()
        }
      } else {
        refreshContentHeight()
      }
      refreshContentHeight()
    } else {
      setHeight('0')
    }
  }, [isExpanded])

  return <View className='collapse-panel'>
    <View className='collapse-panel-header' onClick={() => setIsExpanded(prev => !prev)}>
      <Image src={arrowBottomSolidUrl} className={cls('anchor', { 'anchor-expanded': isExpanded })} />
      {title}
    </View>
    <View className={cls('collapse-panel-body')} style={{ height }}>
      <View ref={bodyRef}>
        {bodyRender(isExpanded, refreshContentHeight)}
      </View>
    </View>
  </View>
}

export default memo(_StyledCollapsePanel)
