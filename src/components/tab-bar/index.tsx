import { Tabbar } from '@taroify/core'
import { Image } from '@tarojs/components'
import { switchTab, useRouter, pxTransform } from '@tarojs/taro'
import useAppStore from '@/store'
import indexSvgUrl from '../../assets/svg/index.svg'
import indexActiveSvgUrl from '../../assets/svg/index-active.svg'
import meSvgUrl from '../../assets/svg/me.svg'
import meActiveSvgUrl from '../../assets/svg/me-active.svg'
import { RoutePath } from '../../constants/route'

const imageStyle = { height: pxTransform(24), width: pxTransform(24) }

// todo
const meActiveLogo = <Image src={meActiveSvgUrl} style={imageStyle} />
const IndexLogo = <Image src={indexSvgUrl} style={imageStyle} />
const IndexActiveLogo = <Image src={indexActiveSvgUrl} style={imageStyle} />
const meLogo = <Image src={meSvgUrl} style={imageStyle} />


export default function TabBar() {
  const { path } = useRouter()
  const activeValue = path.startsWith(RoutePath.Home) ? 'home' : 'me'
  const user = useAppStore(state => state.user)
  const token = useAppStore(state => state.token)

  // 检查用户是否已登录
  const isLoggedIn = () => {
    return token && user && user.id
  }

  // 启动微信授权流程
  const startWeChatAuth = () => {
    const currentUrl = window.location.href.split('?')[0] // 移除现有参数
    const redirectUri = encodeURIComponent(currentUrl)
    const hospitalId = '40384'

    // 步骤1：获取电子健康卡授权码
    const getHealthCodeUrl = `https://h5-health.tengmed.com/h5/tencent/open/card/regist?hospitalId=${hospitalId}&redirect_uri=${redirectUri}&hashMode=1`

    // 保存要跳转到个人中心的标记
    sessionStorage.setItem('pendingAction', 'me')
    window.location.href = getHealthCodeUrl
  }

  const onClick = (value: string) => {
    if (activeValue === value) return
    if (value === 'home') {
      switchTab({ url: RoutePath.Home })
    } else if (value === 'me') {
      // 检查登录状态
      if (isLoggedIn()) {
        switchTab({ url: RoutePath.Me })
      } else {
        // 未登录，启动授权流程
        startWeChatAuth()
      }
    }
  }

  return <Tabbar bordered fixed value={activeValue}>
    <Tabbar.TabItem value='home' icon={path.startsWith(RoutePath.Home) ? IndexActiveLogo : IndexLogo} onClick={() => onClick('home')}>首页</Tabbar.TabItem>
    <Tabbar.TabItem value='me' icon={path.startsWith(RoutePath.Me) ? meActiveLogo : meLogo} onClick={() => onClick('me')}>个人中心</Tabbar.TabItem>
  </Tabbar>
}
