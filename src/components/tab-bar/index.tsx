import { Tabbar } from '@taroify/core'
import { Image } from '@tarojs/components'
import { switchTab, useRouter, pxTransform } from '@tarojs/taro'
import indexSvgUrl from '../../assets/svg/index.svg'
import indexActiveSvgUrl from '../../assets/svg/index-active.svg'
import meSvgUrl from '../../assets/svg/me.svg'
import meActiveSvgUrl from '../../assets/svg/me-active.svg'
import { RoutePath } from '../../constants/route'

const imageStyle = { height: pxTransform(24), width: pxTransform(24) }

// todo
const meActiveLogo = <Image src={meActiveSvgUrl} style={imageStyle} />
const IndexLogo = <Image src={indexSvgUrl} style={imageStyle} />
const IndexActiveLogo = <Image src={indexActiveSvgUrl} style={imageStyle} />
const meLogo = <Image src={meSvgUrl} style={imageStyle} />


export default function TabBar() {
  const { path } = useRouter()
  const activeValue = path.startsWith(RoutePath.Home) ? 'home' : 'me'

  const onClick = (value: string) => {
    if (activeValue === value) return
    if (value === 'home') {
      switchTab({ url: RoutePath.Home })
    } else if (value === 'me') {
      switchTab({ url: RoutePath.Me })
    }
  }

  return <Tabbar bordered fixed value={activeValue}>
    <Tabbar.TabItem value='home' icon={path.startsWith(RoutePath.Home) ? IndexActiveLogo : IndexLogo} onClick={() => onClick('home')}>首页</Tabbar.TabItem>
    <Tabbar.TabItem value='me' icon={path.startsWith(RoutePath.Me) ? meActiveLogo : meLogo} onClick={() => onClick('me')}>个人中心</Tabbar.TabItem>
  </Tabbar>
}
