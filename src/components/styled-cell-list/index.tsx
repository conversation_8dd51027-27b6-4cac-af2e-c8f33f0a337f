import { useMemo } from 'react'
import { Styled333Text, Styled999Text } from '@/components/styled-text'
import { Flex } from '@taroify/core'
import { View } from '@tarojs/components'
import { pxTransform } from '@tarojs/taro'
import './index.scss'

export type CellField = {
  label: string
  field: string
  multi?: boolean
  gap?: boolean
}

type Props = {
  data: any
  fields: CellField[]
  px?: number
}

export default function StyledCellList({ data, fields, px }: Props) {
  const style = useMemo(() => ({ paddingLeft: pxTransform(px === undefined ? 16 : 0), paddingRight: pxTransform(px === undefined ? 16 : 0) }), [px])
  return <View className='styled-cell-list' style={style}>
    {
      fields.map((item, key) => {
        if (item.gap) {
          return <Styled333Text className='mt-12 mb-12 gap-label'>{item.label}</Styled333Text>
        }
        if (item.multi) {
          return <>
            <Styled999Text>{item.label}</Styled999Text>
            <View className='multi-content'>
              {data?.[item.field] || '无'}
            </View>
          </>
        }
        return <Flex justify='space-between' key={key} className='mb-12'>
          <Styled999Text className='flex-no-shrink mr-16'>{item.label}</Styled999Text>
          <Styled333Text align='right'>{data?.[item.field] || '无'}</Styled333Text>
        </Flex>
      })
    }
  </View>
}
