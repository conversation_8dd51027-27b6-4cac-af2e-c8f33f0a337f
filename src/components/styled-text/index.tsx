import { CSSProperties, memo, ReactChild } from 'react'
import { View } from '@tarojs/components'
import { pxTransform } from '@tarojs/taro'

type Props = {
  fontSize?: number
  ellipsis?: number
  color?: string
  small?: boolean
  large?: boolean
  inline?: boolean
  bold?: boolean
  noPlaceholder?: boolean
  children: ReactChild | ReactChild[] | undefined
  className?: string
  align?: 'left' | 'center' | 'right'
}

function _StyledText(props: Props) {
  if (!props.children) {
    return null
  }
  const style: CSSProperties = {}
  if (props.ellipsis === 1) {
    style.overflow = 'hidden'
    style.textOverflow = 'ellipsis'
    style.whiteSpace = 'nowrap'
    style.width = '100%'
  } else if (props.ellipsis === 2) {
    style.overflow = 'hidden'
    style.textOverflow = 'ellipsis'
    style.display = '-webkit-box'
    style.WebkitBoxOrient = 'vertical'
    style.WebkitLineClamp = 2
    style.width = '100%'
  } else if (props.ellipsis === 3) {
    style.overflow = 'hidden'
    style.textOverflow = 'ellipsis'
    style.display = '-webkit-box'
    style.WebkitBoxOrient = 'vertical'
    style.WebkitLineClamp = 3
    style.width = '100%'
  }
  if (props.align) {
    style.textAlign = props.align
  }
  if (props.color) {
    style.color = props.color
  }
  if (props.bold) {
    style.fontWeight = 700
  }
  if (props.fontSize) {
    style.fontSize = pxTransform(props.fontSize)
  } else if (props.small) {
    style.fontSize = pxTransform(12)
  } else if (props.large) {
    style.fontSize = pxTransform(16)
  } else {
    style.fontSize = pxTransform(14)
  }
  if (props.inline) {
    style.display = 'inline-block'
  }
  return <View className={props.className} style={style}>{props.children || (!props.noPlaceholder && '')}</View>
}

const StyledText = memo(_StyledText)

function _StyledWhiteText(props: Props) {
  return <StyledText color='#FFF' {...props} />
}
function _StyledLightWhiteText(props: Props) {
  return <StyledText color='rgba(255, 255, 255, 0.8)' {...props} />
}
function _Styled333Text(props: Props) {
  return <StyledText color='#333' {...props} />
}
function _Styled666Text(props: Props) {
  return <StyledText color='#666' {...props} />
}
function _Styled999Text(props: Props) {
  return <StyledText color='#999' {...props} />
}

export default StyledText
export const StyledWhiteText = memo(_StyledWhiteText)
export const StyledLightWhiteText = memo(_StyledLightWhiteText)
export const Styled333Text = memo(_Styled333Text)
export const Styled666Text = memo(_Styled666Text)
export const Styled999Text = memo(_Styled999Text)
