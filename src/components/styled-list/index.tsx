import { FC, useState, useMemo, useRef, useEffect } from 'react'
import { Empty, Loading, Flex } from '@taroify/core'
import useParentScroll from './useParentScroll'

type Props = {
  className?: string
  loading?: boolean
  data: any[] | undefined
  comp: FC<any>
}

const offset = 100
const pageSize = 10
export default function StyledList({ className, loading, data, comp: ItemComp }: Props) {
  const listWrapperRef = useRef<HTMLDivElement>(null)
  const scrollRef = useParentScroll(listWrapperRef)
  const [pageNum, setPageNum] = useState(1)
  const renderData = useMemo(() => data?.slice(0, pageNum * pageSize), [data, pageNum])
  const placeholderRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    let tick = true
    const scroll = scrollRef.current
    const onScroll = () => {
      if (tick) {
        tick = false
        window.requestAnimationFrame(() => {
          const scrollBottom = scroll?.getBoundingClientRect()?.bottom || 0
          const placeholderBottom = placeholderRef.current?.getBoundingClientRect()?.bottom || 0
          if (placeholderBottom - scrollBottom <= offset) {
            setPageNum(prev => prev + 1)
          }
          tick = true
        })
      }
    }
    scroll?.addEventListener("scroll", onScroll)
    return () => {
      scroll?.removeEventListener("scroll", onScroll)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return <div className={className} ref={listWrapperRef}>
    {
      loading ?
        loading && <Flex align='center' justify='center' style={{ marginTop: '4rem' }}><Loading /></Flex> :
        renderData && renderData.length > 0 ?
          renderData.map((item, idx) => <ItemComp key={idx} {...item} />) :
          <Empty>
            <Empty.Image />
            <Empty.Description>暂无数据</Empty.Description>
          </Empty>
    }
    <div ref={placeholderRef}></div>
  </div>
}
