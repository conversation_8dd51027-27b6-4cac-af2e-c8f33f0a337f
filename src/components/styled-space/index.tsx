import { ReactChild, Children, memo, CSSProperties } from 'react'
import { Flex } from '@taroify/core'
import { pxTransform } from '@tarojs/taro'

type Props = {
  children: ReactChild | ReactChild[]
  gapX?: number
  gapY?: number
  className?: string
}

 function _StyledSpace({ children, gapX, gapY, className }: Props) {
  const style: CSSProperties = { marginRight: pxTransform(gapX || 8) }
  if (gapY) {
    style.marginBottom = pxTransform(gapY)
  }
  return <Flex align='center' wrap='wrap' className={className}>
    {
      Children.map(children, (item, idx) => {
        if (typeof item === 'object' && 'children' in item.props) {
          return item.props.children ? <Flex.Item style={style} key={idx}>{item}</Flex.Item> : null
        }
        return item ? <Flex.Item style={style} key={idx}>{item}</Flex.Item> : null
      })
    }
  </Flex>
}
export default memo(_StyledSpace)
