import { useEffect } from 'react'
import { Navbar } from '@taroify/core'
import { View } from '@tarojs/components'
import { navigateBack, pxTransform } from '@tarojs/taro'

const navLeftStyle = { paddingLeft: pxTransform(16) }

type Props = {
  title: string
  onClickBack?: () => void
}

export default function StyledNavbar({ title, onClickBack }: Props) {
  useEffect(() => {
    const prevTitle = document.title
    return () => {
      if (prevTitle) {
        document.title = prevTitle
      }
    }
  }, [])
  useEffect(() => {
    document.title = title
  }, [title])
  return <>
    <Navbar title={title} className='style-nav'>
      <Navbar.NavLeft style={navLeftStyle} onClick={() => onClickBack ? onClickBack() : navigateBack()}></Navbar.NavLeft>
    </Navbar>
    <View className='taroify-hairline--bottom'></View>
  </>
}
