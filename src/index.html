<!DOCTYPE html>
<html>
<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
  <meta content="width=device-width,initial-scale=1,user-scalable=no" name="viewport">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-touch-fullscreen" content="yes">
  <meta name="format-detection" content="telephone=no,address=no">
  <meta name="apple-mobile-web-app-status-bar-style" content="white">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" >
  <title>居民健康档案</title>
  <!-- 超早期URL参数捕获脚本 - 必须在所有其他脚本之前执行 -->
  <script>
    // 内联超早期捕获脚本，确保最早执行
    (function() {
      'use strict';

      const STORAGE_KEY = 'taro_ultra_early_params';
      const timestamp = Date.now();
      const originalUrl = window.location.href;

      console.log(`🚀 [${timestamp}] 超早期捕获 - 原始URL:`, originalUrl);

      // 立即保存原始URL
      try {
        sessionStorage.setItem('original_url_' + timestamp, originalUrl);
        localStorage.setItem('original_url_backup', originalUrl);
      } catch (e) {
        console.warn('无法保存原始URL:', e);
      }

      // 多重参数解析
      function parseParams(url) {
        const params = {};

        // 方法1: 正则表达式匹配
        const patterns = {
          healthCode: /[?&]healthCode=([^&#]+)/i,
          code: /[?&]code=([^&#]+)/i,
          state: /[?&]state=([^&#]+)/i
        };

        for (const [key, pattern] of Object.entries(patterns)) {
          const match = url.match(pattern);
          if (match) {
            try {
              params[key] = decodeURIComponent(match[1]);
            } catch (e) {
              params[key] = match[1];
            }
          }
        }

        console.log('🎯 正则解析结果:', params);

        // 方法2: 手动分割解析
        if (url.includes('?')) {
          const parts = url.split('?');
          for (let i = 1; i < parts.length; i++) {
            const queryPart = parts[i].split('#')[0]; // 移除hash部分
            const pairs = queryPart.split('&');

            for (const pair of pairs) {
              const [key, value] = pair.split('=');
              if (key && value && ['healthCode', 'code', 'state'].includes(key)) {
                try {
                  params[key] = decodeURIComponent(value);
                } catch (e) {
                  params[key] = value;
                }
              }
            }
          }
        }

        console.log('🔍 手动解析结果:', params);
        return params;
      }

      const params = parseParams(originalUrl);

      if (Object.keys(params).length > 0) {
        const captureData = {
          ...params,
          timestamp,
          originalUrl,
          userAgent: navigator.userAgent,
          captureMethod: 'ultra_early_inline',
          documentReadyState: document.readyState
        };

        console.log('💾 超早期捕获成功:', captureData);

        // 保存到多个位置
        try {
          sessionStorage.setItem(STORAGE_KEY, JSON.stringify(captureData));
          sessionStorage.setItem('authParams', JSON.stringify(captureData));
          localStorage.setItem(STORAGE_KEY + '_backup', JSON.stringify(captureData));

          // 保存到全局变量
          window.__ULTRA_EARLY_PARAMS__ = captureData;

          // 保存到DOM属性
          document.documentElement.setAttribute('data-auth-params', JSON.stringify(captureData));

        } catch (e) {
          console.error('保存参数失败:', e);
        }
      } else {
        console.log('❌ 超早期捕获 - 未发现参数');
      }

      // 导出获取函数到全局
      window.getUltraEarlyParams = function() {
        // 优先从内存获取
        if (window.__ULTRA_EARLY_PARAMS__) {
          return window.__ULTRA_EARLY_PARAMS__;
        }

        // 从DOM属性获取
        try {
          const domData = document.documentElement.getAttribute('data-auth-params');
          if (domData) {
            return JSON.parse(domData);
          }
        } catch (e) {
          console.warn('从DOM读取失败:', e);
        }

        // 从sessionStorage获取
        try {
          const stored = sessionStorage.getItem(STORAGE_KEY) || sessionStorage.getItem('authParams');
          if (stored) {
            return JSON.parse(stored);
          }
        } catch (e) {
          console.warn('从sessionStorage读取失败:', e);
        }

        // 从localStorage获取
        try {
          const stored = localStorage.getItem(STORAGE_KEY + '_backup');
          if (stored) {
            return JSON.parse(stored);
          }
        } catch (e) {
          console.warn('从localStorage读取失败:', e);
        }

        return null;
      };

    })();
  </script>
  <script><%= htmlWebpackPlugin.options.script %></script>
  <% if (htmlWebpackPlugin.options.env.vConsole) { %>
    <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
    <script>var vConsole = new window.VConsole();</script>
  <% } %>
</head>
<body>
  <div id="app"></div>
</body>
</html>
