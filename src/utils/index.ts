import { useState, useEffect, useMemo } from 'react'
import { useRouter } from '@tarojs/taro'
import dayjs from 'dayjs'

export const formatDate = (date: string) => {
  return date ? dayjs(date).format('YYYY-MM-DD') : ''
}

export const stringifyURL = (obj: Record<string, string>) => (new URLSearchParams(obj)).toString()
export const parseSearchParams = (obj: Partial<Record<string, string>>) => {
  const ret: Record<string, string> = {}
  for (const key in obj) {
    if (obj[key] && obj[key] !== 'null' && obj[key] !== 'undefined') {
      ret[key] = decodeURIComponent(obj[key]!)
    }
  }
  return ret
}
export const useTitle = (title: string) => {
  const [isSet, setIsSet] = useState(false)
  useEffect(() => {
    document.title = title
    setIsSet(true)
  }, [title])
  return isSet
}
export const useRouteSearch = <T = any>() => {
  const router = useRouter()
  const search = useMemo(() => parseSearchParams(router.params), [router.params])
  return search as T
}

type Rule = {
  required?: boolean
  pattern?: RegExp
  message: string
}

export const useVerification = (rules: Rule[], initVal?: string) => {
  const [value, setValue] = useState(initVal || '')
  const [error, setError] = useState<string[]>([])
  useEffect(() => {
    const errs: string[] = []
    for (const rule of rules) {
      if (rule.required && !value) {
        errs.push(rule.message)
      }
      if (rule.pattern && !rule.pattern.test(value)) {
        errs.push(rule.message)
      }
    }
    setError(errs)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value])

  return [value, setValue, error, setError] as const
}

