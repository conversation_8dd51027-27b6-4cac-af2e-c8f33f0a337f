import {
  navigateTo as navigateToOrigin,
  navigateBack as navigateBackOrigin,
  redirectTo as redirectToOrigin,
  switchTab as switchTabOrigin
} from '@tarojs/taro'
import { stringifyURL } from '@/utils'
import { debounce } from './lodash-polyfill'


const wait = 500
const options = {
  leading: true,
  trailing: false
}
export const navigateTo = debounce((url: string, query?: Record<string, string>) => navigateToOrigin({ url: `${url}${query ? ('?' + stringifyURL(query)) : ''}` }), wait, options)
export const navigateBack = debounce(navigateBackOrigin, wait, options)
export const redirectTo = debounce((url: string) => redirectToOrigin({ url }), wait, options)
export const switchTab = debounce((url: string) => switchTabOrigin({ url }), wait, options)

