import { create } from 'zustand'
import { getEnv, ENV_TYPE } from '@tarojs/taro'
import { login, getUserInfo, UserInfo } from '@/api/user'
import { redirectTo } from '@/utils/router'
import { RoutePath } from '@/constants/route'

type AppStore = {
  user: UserInfo
  token: string
  errorReason: string
  init: () => Promise<boolean>
  initUser: () => Promise<void>
  login: (data: any) => Promise<void>
  logout: () => void
}

const useAppStore = create<AppStore>((set, get) => ({
  user: {} as any,
  token: '',
  errorReason: '',
  init: async () => {
    const env = getEnv()
    if (env === ENV_TYPE.WEB) {
      const ua = navigator.userAgent.toLowerCase()
      if (ua.includes('android') || ua.includes('iphone')) {
        const tokenCache = localStorage.getItem('token')
        if (tokenCache) {
          set({ token: tokenCache })
          get().initUser()
        } else {
          get().logout()
        }
      } else {
        set({ errorReason: 'device' })
        return false
      }
    }
    return true
  },
  async initUser() {
    try {
      const user = await getUserInfo()
      set({
        user
      })
    } catch {
      set({
        user: {} as any
      })
    }
  },
  async login(data: any) {
    try {
      const token = await login(data)
      set({ token })
      localStorage.setItem('token', token)
      get().initUser()
      redirectTo(RoutePath.Home)
    } catch {
      set({
        token: ''
      })
    }
  },
  logout() {
    localStorage.removeItem('token')
    set({
      token: '',
      user: {} as any
    })
    if (!window.location.hash.includes('pages/index/index')) {
      redirectTo(RoutePath.Home)
    }
  }
}))

export const useAppInit = () => useAppStore(state => state.init)
export const useUser = () => useAppStore(state => state.user)
export const getLogoutOut = () => useAppStore.getState().logout
export const getTokenOut = () => useAppStore.getState().token
export const getUser = () => useAppStore.getState().user

export default useAppStore
