import { PropsWithChildren } from 'react'
import { useLaunch } from '@tarojs/taro'
import { View } from '@tarojs/components'
import useAppStore from '@/store'

import './base.scss'
import './taroify.scss'
import './app.scss'

function App({ children }: PropsWithChildren<any>) {
  const [errorReason, init] = useAppStore(state => [state.errorReason, state.init])
  console.log('app rerender')
  useLaunch(() => {
    console.log('app launch')
    init()
  })

  if (errorReason === 'device') {
    return <>
      <View>请从手机端打开</View>
      <View style={{ display: 'none' }}>{children}</View>
    </>
  }

  return children
}

export default App
