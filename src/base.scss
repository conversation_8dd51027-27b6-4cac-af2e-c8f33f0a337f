* {
  box-sizing: border-box;
}

.p-4 {
  padding: 4px;
}
.py-4 {
  padding-top: 4px;
  padding-bottom: 4px;
}
.px-4 {
  padding-left: 4px;
  padding-right: 4px;
}
.pt-4 {
  padding-top: 4px;
}
.pb-4 {
  padding-bottom: 4px;
}
.pl-4 {
  padding-left: 4px;
}
.pr-4 {
  padding-right: 4px;
}

.p-8 {
  padding: 8px;
}
.py-8 {
  padding-top: 8px;
  padding-bottom: 8px;
}
.px-8 {
  padding-left: 8px;
  padding-right: 8px;
}
.pt-8 {
  padding-top: 8px;
}
.pb-8 {
  padding-bottom: 8px;
}
.pl-8 {
  padding-left: 8px;
}
.pr-8 {
  padding-right: 8px;
}

.p-12 {
  padding: 12px;
}
.py-12 {
  padding-top: 12px;
  padding-bottom: 12px;
}
.px-12 {
  padding-left: 12px;
  padding-right: 12px;
}
.pt-12 {
  padding-top: 12px;
}
.pb-12 {
  padding-bottom: 12px;
}
.pl-12 {
  padding-left: 12px;
}
.pr-12 {
  padding-right: 12px;
}

.p-16 {
  padding: 16px;
}
.py-16 {
  padding-top: 16px;
  padding-bottom: 16px;
}
.px-16 {
  padding-left: 16px;
  padding-right: 16px;
}
.pt-16 {
  padding-top: 16px;
}
.pb-16 {
  padding-bottom: 16px;
}
.pl-16 {
  padding-left: 16px;
}
.pr-16 {
  padding-right: 16px;
}

.m-4 {
  margin: 4px;
}
.my-4 {
  margin-top: 4px;
  margin-bottom: 4px;
}
.mx-4 {
  margin-left: 4px;
  margin-right: 4px;
}
.mt-4 {
  margin-top: 4px;
}
.mb-4 {
  margin-bottom: 4px;
}
.ml-4 {
  margin-left: 4px;
}
.mr-4 {
  margin-right: 4px;
}

.m-8 {
  margin: 8px;
}
.my-8 {
  margin-top: 8px;
  margin-bottom: 8px;
}
.mx-8 {
  margin-left: 8px;
  margin-right: 8px;
}
.mt-8 {
  margin-top: 8px;
}
.mb-8 {
  margin-bottom: 8px;
}
.ml-8 {
  margin-left: 8px;
}
.mr-8 {
  margin-right: 8px;
}

.m-12 {
  margin: 12px;
}
.my-12 {
  margin-top: 12px;
  margin-bottom: 12px;
}
.mx-12 {
  margin-left: 12px;
  margin-right: 12px;
}
.mt-12 {
  margin-top: 12px;
}
.mb-12 {
  margin-bottom: 12px;
}
.ml-12 {
  margin-left: 12px;
}
.mr-12 {
  margin-right: 12px;
}

.m-16 {
  margin: 16px;
}
.my-16 {
  margin-top: 16px;
  margin-bottom: 16px;
}
.mx-16 {
  margin-left: 16px;
  margin-right: 16px;
}
.mt-16 {
  margin-top: 16px;
}
.mb-16 {
  margin-bottom: 16px;
}
.ml-16 {
  margin-left: 16px;
}
.mr-16 {
  margin-right: 16px;
}
.flexAround {
  display: flex;
  justify-content: space-around;
  flex-wrap: nowrap;
}
.flexBetween {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
}
.flex {
  display: flex;
  flex-wrap: nowrap;
}
.pl-32 {
  padding-left: 32px;
}
.relative {
  position: relative;
}
.flex-no-shrink {
  flex-shrink: 0;
}

.line {
  height: 1px;
  width: 100%;
  background: #eeeeee;
}
.box-shadow {
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
}
.border-radius-8 {
  border-radius: 8px;
}
