/* Copyright 2016 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

 * {
  padding: 0;
  margin: 0;
}

html {
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-size: 10px;
}

header {
  background-color: rgba(244, 244, 244, 1);
}

header h1 {
  border-bottom: 1px solid rgba(216, 216, 216, 1);
  color: rgba(133, 133, 133, 1);
  font-size: 23px;
  font-style: italic;
  font-weight: normal;
  overflow: hidden;
  padding: 10px;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}

body {
  background: url(images/document_bg.png);
  color: rgba(255, 255, 255, 1);
  font-family: sans-serif;
  font-size: 10px;
  height: 100%;
  width: 100%;
  overflow: hidden;
  padding-bottom: 5rem;
}

section {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-size: 2rem;
}

footer {
  background-image: url(images/toolbar_background.png);
  height: 4rem;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  box-shadow: 0 -0.2rem 0.5rem rgba(50, 50, 50, 0.75);
}

.toolbarButton {
  display: block;
  padding: 0;
  margin: 0;
  border-width: 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: rgba(0, 0, 0, 0);
}

.toolbarButton.pageUp {
  position: absolute;
  width: 18%;
  height: 100%;
  left: 0;
  background-image: url(images/icon_previous_page.png);
  background-size: 2rem;
}

.toolbarButton.pageDown {
  position: absolute;
  width: 18%;
  height: 100%;
  left: 18%;
  background-image: url(images/icon_next_page.png);
  background-size: 2rem;
}

#pageNumber {
  -moz-appearance: textfield; /* hides the spinner in moz */
  position: absolute;
  width: 28%;
  height: 100%;
  left: 36%;
  text-align: center;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 1);
  background-image: url(images/div_line_left.png),
    url(images/div_line_right.png);
  background-repeat: no-repeat;
  background-position: left, right;
  background-size: 0.2rem, 0.2rem;
}

.toolbarButton.zoomOut {
  position: absolute;
  width: 18%;
  height: 100%;
  left: 64%;
  background-image: url(images/icon_zoom_out.png);
  background-size: 2.4rem;
}

.toolbarButton.zoomIn {
  position: absolute;
  width: 18%;
  height: 100%;
  left: 82%;
  background-image: url(images/icon_zoom_in.png);
  background-size: 2.4rem;
}

.toolbarButton[disabled] {
  opacity: 0.3;
}

.hidden {
  display: none;
}
[hidden] {
  display: none !important;
}

#viewerContainer {
  position: absolute;
  overflow: auto;
  width: 100%;
  inset: 0 0 1rem;
}

canvas {
  margin: auto;
  display: block;
}

.pdfViewer .page .loadingIcon {
  width: 2.9rem;
  height: 2.9rem;
  background: url("images/spinner.png") no-repeat left top / 38rem;
  border: medium none;
  animation: 1s steps(10, end) 0s normal none infinite moveDefault;
  display: block;
  position: absolute;
  top: calc((100% - 2.9rem) / 2);
  left: calc((100% - 2.9rem) / 2);
}

@keyframes moveDefault {
  from {
    background-position: 0 top;
  }

  to {
    background-position: -39rem top;
  }
}

#loadingBar {
  /* Define this variable here, and not in :root, to avoid reflowing the
     entire viewer when updating progress (see issue 15958). */
  --progressBar-percent: 0%;

  position: relative;
  height: 0.6rem;
  background-color: rgba(51, 51, 51, 1);
  border-bottom: 1px solid rgba(51, 51, 51, 1);
}

#loadingBar .progress {
  position: absolute;
  left: 0;
  width: 100%;
  transform: scaleX(var(--progressBar-percent));
  transform-origin: 0 0;
  height: 100%;
  background-color: rgba(221, 221, 221, 1);
  overflow: hidden;
  transition: transform 200ms;
}

@keyframes progressIndeterminate {
  0% {
    transform: translateX(0%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}

#loadingBar.indeterminate .progress {
  transform: none;
  background-color: rgba(153, 153, 153, 1);
  transition: none;
}

#loadingBar.indeterminate .progress .glimmer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 5rem;
  background-image: linear-gradient(
    to right,
    rgba(153, 153, 153, 1) 0%,
    rgba(255, 255, 255, 1) 50%,
    rgba(153, 153, 153, 1) 100%
  );
  background-size: 100% 100%;
  background-repeat: no-repeat;
  animation: progressIndeterminate 2s linear infinite;
}

#errorWrapper {
  background: none repeat scroll 0 0 rgba(255, 85, 85, 1);
  color: rgba(255, 255, 255, 1);
  text-align: center;
  left: 0;
  position: absolute;
  right: 0;
  top: 3.2rem;
  z-index: 1000;
  padding: 0.3rem;
  font-size: 0.8em;
}

#errorMessageLeft {
  float: left;
}

#errorMessageRight {
  float: right;
}

#errorMoreInfo {
  background-color: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
  padding: 0.3rem;
  margin: 0.3rem;
  width: 98%;
}
