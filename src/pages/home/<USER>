import { View, Image } from '@tarojs/components'
import { Flex } from '@taroify/core'
import { showToast, showLoading, hideLoading } from '@tarojs/taro'
import TabBar from '@/components/tab-bar'
import { navigateTo, switchTab } from '@/utils/router'
import { RoutePath } from '@/constants/route'
import { useMount } from 'ahooks'
import useAppStore from '@/store'
import { getHealthCardInfo, healthCardLogin } from '@/api/user'
import indexTopBgUrl from '@/assets/png/index-top-bg.png'
import jdxxUrl from '@/assets/svg/jdxx.svg'
import ylfwUrl from '@/assets/svg/ylfw.svg'
import jktjUrl from '@/assets/svg/jktj.svg'
import jkjyUrl from '@/assets/svg/jkjy.svg'
import ggwsUrl from '@/assets/svg/ggws.svg'
import StyledText from '@/components/styled-text'
import { useState } from 'react'


import './index.scss'

type EntryItem = {
  logoUrl: string
  path: string
}

const entryList: EntryItem[] = [
  { logoUrl: jdxxUrl, path: RoutePath.FilingInformation },
  { logoUrl: ylfwUrl, path: RoutePath.MedicalService },
  { logoUrl: jktjUrl, path: RoutePath.HealthCheck },
  { logoUrl: jkjyUrl, path: RoutePath.HealthEducation },
  { logoUrl: ggwsUrl, path: RoutePath.PublicHealth }
]

// 使用立即执行函数在模块加载时捕获参数
(() => {
  // 等待DOM加载完成
  const captureParams = () => {
    const currentUrl = window.location.href
    console.log('🔍 捕获URL:', currentUrl)

    // 多种方式解析参数
    let healthCode: string | null = null
    let code: string | null = null
    let state: string | null = null

    // 方法1: 从hash部分解析
    if (window.location.hash.includes('?')) {
      const hashParams = new URLSearchParams(window.location.hash.split('?')[1])
      healthCode = hashParams.get('healthCode')
      code = hashParams.get('code')
      state = hashParams.get('state')
      console.log('📍 从hash解析:', { healthCode, code, state })
    }

    // 方法2: 从search部分解析
    if (!healthCode && !code && !state && window.location.search) {
      const searchParams = new URLSearchParams(window.location.search)
      healthCode = searchParams.get('healthCode')
      code = searchParams.get('code')
      state = searchParams.get('state')
      console.log('📍 从search解析:', { healthCode, code, state })
    }

    // 方法3: 手动正则匹配
    if (!healthCode && !code && !state) {
      const urlStr = currentUrl
      const healthCodeMatch = urlStr.match(/[?&]healthCode=([^&]+)/)
      const codeMatch = urlStr.match(/[?&]code=([^&]+)/)
      const stateMatch = urlStr.match(/[?&]state=([^&]+)/)

      healthCode = healthCodeMatch ? decodeURIComponent(healthCodeMatch[1]) : null
      code = codeMatch ? decodeURIComponent(codeMatch[1]) : null
      state = stateMatch ? decodeURIComponent(stateMatch[1]) : null
      console.log('📍 从正则解析:', { healthCode, code, state })
    }

    // 如果有参数，立即保存
    if (healthCode || code || state) {
      const authParams = {
        healthCode,
        code,
        state,
        timestamp: Date.now(),
        originalUrl: currentUrl
      }
      sessionStorage.setItem('authParams', JSON.stringify(authParams))
      console.log('💾 参数已保存:', authParams)
      return true
    }

    return false
  }

  // 立即尝试捕获
  if (!captureParams()) {
    // 如果立即捕获失败，等待一小段时间再试
    setTimeout(captureParams, 100)
  }
})()

export default function Home() {
  const user = useAppStore((state) => state.user)
  const token = useAppStore((state) => state.token)

  const [url, setUrl] = useState(window.location.href)
  const [urlHistory, setUrlHistory] = useState<string[]>([])

  useMount(() => {
    // 记录URL变化历史
    const initialUrl = window.location.href
    console.log('🏠 组件挂载时URL:', initialUrl)
    setUrl(initialUrl)
    setUrlHistory(prev => [...prev, `挂载: ${initialUrl}`])

    // 监听URL变化
    const originalPushState = window.history.pushState
    const originalReplaceState = window.history.replaceState

    window.history.pushState = function(...args: any[]) {
      originalPushState.apply(window.history, args)
      const newUrl = window.location.href
      console.log('🔄 pushState变化:', newUrl)
      setUrl(newUrl)
      setUrlHistory(prev => [...prev, `pushState: ${newUrl}`])
    }

    window.history.replaceState = function(...args: any[]) {
      originalReplaceState.apply(window.history, args)
      const newUrl = window.location.href
      console.log('🔄 replaceState变化:', newUrl)
      setUrl(newUrl)
      setUrlHistory(prev => [...prev, `replaceState: ${newUrl}`])
    }

    // 监听popstate事件
    const handlePopState = () => {
      const newUrl = window.location.href
      console.log('🔄 popstate变化:', newUrl)
      setUrl(newUrl)
      setUrlHistory(prev => [...prev, `popstate: ${newUrl}`])
    }

    window.addEventListener('popstate', handlePopState)

    // 检查是否有授权回调参数
    handleAuthCallback()

    // 清理函数
    return () => {
      window.history.pushState = originalPushState
      window.history.replaceState = originalReplaceState
      window.removeEventListener('popstate', handlePopState)
    }
  })

  // 检查用户是否已登录
  const isLoggedIn = () => {
    return token && user && user.id
  }

  // 处理授权回调
  const handleAuthCallback = async () => {
    // 优先从sessionStorage中读取参数
    let healthCode: string | null = null
    let code: string | null = null
    let state: string | null = null

    const savedParams = sessionStorage.getItem('authParams')
    if (savedParams) {
      const authParams = JSON.parse(savedParams)
      healthCode = authParams.healthCode
      code = authParams.code
      state = authParams.state
      console.log('从sessionStorage读取参数:', authParams)
    } else {
      // 如果sessionStorage中没有，再从URL解析
      const urlParams = new URLSearchParams(window.location.search || window.location.hash.split('?')[1] || '')
      healthCode = urlParams.get('healthCode')
      code = urlParams.get('code')
      state = urlParams.get('state')
      console.log('从URL解析参数:', { healthCode, code, state })
    }

    // 调试信息
    console.log('当前URL:', window.location.href)
    console.log('最终参数:', { healthCode, code, state })

    showToast({
      title: `healthCode: ${healthCode}, code: ${code}`,
      icon: 'none'
    })

    if (healthCode && !code) {
      // 第一步：获得健康卡授权码，现在需要获取微信授权码
      const currentUrl = window.location.href
      const redirectUri = encodeURIComponent(currentUrl)
      const appId = process.env.TARO_APP_JKZJ_APPID

      const getWeChatCodeUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&state=${healthCode}&hashMode=1#wechat_redirect`
      window.location.href = getWeChatCodeUrl
      return
    }

    if (code && state) {
      // 第二步：获得微信授权码，使用state中的healthCode进行登录
      try {
        await performHealthCardLogin(code, state)

        // 登录成功后，清除授权参数并检查是否有待执行的操作
        sessionStorage.removeItem('authParams')
        const pendingAction = sessionStorage.getItem('pendingAction')
        sessionStorage.removeItem('pendingAction')

        if (pendingAction === 'me') {
          // 跳转到个人中心
          switchTab(RoutePath.Me)
        }
        // 如果是查询功能，页面会自动刷新，用户可以重新点击
      } catch (error) {
        console.error('授权登录失败:', error)
        sessionStorage.removeItem('authParams')
        sessionStorage.removeItem('pendingAction')
      }
    }
  }

  // 执行健康卡登录流程
  const performHealthCardLogin = async (mpCode: string, healthCode: string) => {
    try {
      showLoading({ title: '登录中...' })

      // 步骤3：获取健康卡信息
      const healthCardInfo = await getHealthCardInfo({
        mpCode,
        healthCode
      })

      // 步骤4：执行登录
      const loginResult = await healthCardLogin({
        username: healthCardInfo.name,
        telephone: healthCardInfo.phone1,
        idCard: healthCardInfo.idNumber
      })

      hideLoading()

      // 登录成功，保存token并初始化用户信息
      localStorage.setItem('token', loginResult)

      // 使用store的setState方法更新token
      useAppStore.setState({ token: loginResult })

      // 初始化用户信息
      const { initUser } = useAppStore.getState()
      await initUser()

      showToast({
        title: '登录成功',
        icon: 'success'
      })
    } catch (error: any) {
      hideLoading()

      // 处理登录失败的情况
      if (error.message && error.message.includes('登录失败，未匹配到该患者建档信息')) {
        showToast({
          title: error.message,
          icon: 'error',
          duration: 3000
        })
      } else {
        showToast({
          title: '登录失败，请重试',
          icon: 'error'
        })
      }
      throw error
    }
  }

  // 启动微信授权流程
  const startWeChatAuth = () => {
    const currentUrl = window.location.href
    const redirectUri = encodeURIComponent(currentUrl)
    const hospitalId = '40384'

    // 步骤1：获取电子健康卡授权码
    const getHealthCodeUrl = `https://h5-health.tengmed.com/h5/tencent/open/card/regist?hospitalId=${hospitalId}&redirect_uri=${redirectUri}&hashMode=1`
    showToast({
      title: `getHealthCodeUrl: ${getHealthCodeUrl}`,
      icon: 'none'
    })
    // 直接跳转到健康卡授权，后续流程在回调中处理
    setTimeout(() => {
      window.location.href = getHealthCodeUrl
    }, 1000)
  }

  // 处理需要登录的操作
  const handleActionWithAuth = async (action: () => void) => {
    if (isLoggedIn()) {
      // 已登录，直接执行操作
      action()
    } else {
      // 未登录，保存操作并启动授权流程
      sessionStorage.setItem('pendingAction', 'true')
      startWeChatAuth()
    }
  }

  // 查询功能按钮点击处理
  function onEntryClick(entry: EntryItem) {
    handleActionWithAuth(() => {
      navigateTo(entry.path)
    })
  }

  // 获取当前保存的参数用于调试显示
  const savedParams = sessionStorage.getItem('authParams')
  const authParams = savedParams ? JSON.parse(savedParams) : null

  return (
    <View className='home'>
      <View style={{ padding: '10px', fontSize: '12px', backgroundColor: '#f0f0f0', maxHeight: '200px', overflow: 'auto' }}>
        <StyledText>当前URL: {url}</StyledText>
        <br />
        <StyledText>保存的参数: {savedParams || '无'}</StyledText>
        {authParams && (
          <>
            <br />
            <StyledText>healthCode: {authParams.healthCode || '无'}</StyledText>
            <br />
            <StyledText>code: {authParams.code || '无'}</StyledText>
            <br />
            <StyledText>state: {authParams.state || '无'}</StyledText>
            <br />
            <StyledText>原始URL: {authParams.originalUrl || '无'}</StyledText>
          </>
        )}
        <br />
        <StyledText>URL历史:</StyledText>
        {urlHistory.slice(-5).map((historyItem, index) => (
          <div key={index} style={{ fontSize: '10px' }}>
            <StyledText>{historyItem}</StyledText>
          </div>
        ))}
      </View>
      <Image className='home-top-bg' src={indexTopBgUrl} />
      <View className='home-main'>
        <Flex className='entry-list' justify='space-between' wrap='wrap'>
          {entryList.map((entry) => (
            <Image key={entry.path} className='entry-image' src={entry.logoUrl} onClick={() => onEntryClick(entry)} />
          ))}
        </Flex>
      </View>
      <TabBar />
    </View>
  )
}
