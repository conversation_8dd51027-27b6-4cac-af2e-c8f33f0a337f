import { View, Image } from '@tarojs/components'
import { Flex } from '@taroify/core'
import { showToast, showLoading, hideLoading } from '@tarojs/taro'
import TabBar from '@/components/tab-bar'
import { navigateTo, switchTab } from '@/utils/router'
import { RoutePath } from '@/constants/route'
import { useMount } from 'ahooks'
import useAppStore from '@/store'
import { getHealthCardInfo, healthCardLogin } from '@/api/user'
import indexTopBgUrl from '@/assets/png/index-top-bg.png'
import jdxxUrl from '@/assets/svg/jdxx.svg'
import ylfwUrl from '@/assets/svg/ylfw.svg'
import jktjUrl from '@/assets/svg/jktj.svg'
import jkjyUrl from '@/assets/svg/jkjy.svg'
import ggwsUrl from '@/assets/svg/ggws.svg'
import StyledText from '@/components/styled-text'
import { useState } from 'react'


import './index.scss'

type EntryItem = {
  logoUrl: string
  path: RoutePath
}

const entryList: EntryItem[] = [
  { logoUrl: jdxxUrl, path: RoutePath.FilingInformation },
  { logoUrl: ylfwUrl, path: RoutePath.MedicalService },
  { logoUrl: jktjUrl, path: RoutePath.HealthCheck },
  { logoUrl: jkjyUrl, path: RoutePath.HealthEducation },
  { logoUrl: ggwsUrl, path: RoutePath.PublicHealth }
] as const

// 在脚本加载的最早阶段捕获URL参数
// 这个函数会在任何React组件或Taro路由初始化之前执行
const EARLY_CAPTURE_KEY = 'taro_early_url_params'

// 立即执行的参数捕获
const earlyCapture = (() => {
  const timestamp = Date.now()
  const originalUrl = window.location.href

  console.log(`� [${timestamp}] 脚本加载时URL:`, originalUrl)

  // 多重解析策略
  const parseUrlParams = (url: string) => {
    const results: any = {}

    // 策略1: 正则表达式直接匹配（最可靠）
    const patterns = {
      healthCode: /[?&]healthCode=([^&#]+)/,
      code: /[?&]code=([^&#]+)/,
      state: /[?&]state=([^&#]+)/
    }

    Object.entries(patterns).forEach(([key, pattern]) => {
      const match = url.match(pattern)
      if (match) {
        results[key] = decodeURIComponent(match[1])
      }
    })

    console.log('🎯 正则解析结果:', results)

    // 策略2: URLSearchParams解析hash部分
    if (url.includes('#') && url.includes('?')) {
      try {
        const hashPart = url.split('#')[1]
        if (hashPart && hashPart.includes('?')) {
          const queryPart = hashPart.split('?')[1]
          const params = new URLSearchParams(queryPart)

          ['healthCode', 'code', 'state'].forEach(key => {
            const value = params.get(key)
            if (value && !results[key]) {
              results[key] = value
            }
          })

          console.log('� Hash解析结果:', results)
        }
      } catch (e) {
        console.warn('Hash解析失败:', e)
      }
    }

    // 策略3: URLSearchParams解析search部分
    if (url.includes('?') && !url.includes('#?')) {
      try {
        const searchPart = url.split('?')[1].split('#')[0]
        const params = new URLSearchParams(searchPart)

        ['healthCode', 'code', 'state'].forEach(key => {
          const value = params.get(key)
          if (value && !results[key]) {
            results[key] = value
          }
        })

        console.log('� Search解析结果:', results)
      } catch (e) {
        console.warn('Search解析失败:', e)
      }
    }

    return results
  }

  const params = parseUrlParams(originalUrl)

  // 如果解析到参数，立即保存到多个位置
  if (Object.keys(params).length > 0) {
    const captureData = {
      ...params,
      timestamp,
      originalUrl,
      userAgent: navigator.userAgent,
      captureMethod: 'early'
    }

    // 保存到sessionStorage
    try {
      sessionStorage.setItem(EARLY_CAPTURE_KEY, JSON.stringify(captureData))
      sessionStorage.setItem('authParams', JSON.stringify(captureData))
      console.log('� 参数已保存到sessionStorage:', captureData)
    } catch (e) {
      console.error('sessionStorage保存失败:', e)
    }

    // 保存到全局变量作为备份
    (window as any).__TARO_AUTH_PARAMS__ = captureData

    // 保存到localStorage作为备份
    try {
      localStorage.setItem(EARLY_CAPTURE_KEY, JSON.stringify(captureData))
      console.log('💾 参数已保存到localStorage作为备份')
    } catch (e) {
      console.warn('localStorage保存失败:', e)
    }

    return captureData
  }

  console.log('❌ 未发现URL参数')
  return null
})()

// 导出捕获结果供组件使用
const getEarlyCapturedParams = () => {
  // 优先从内存中获取
  if (earlyCapture) {
    return earlyCapture
  }

  // 从全局变量获取
  if ((window as any).__TARO_AUTH_PARAMS__) {
    return (window as any).__TARO_AUTH_PARAMS__
  }

  // 从sessionStorage获取
  try {
    const stored = sessionStorage.getItem(EARLY_CAPTURE_KEY) || sessionStorage.getItem('authParams')
    if (stored) {
      return JSON.parse(stored)
    }
  } catch (e) {
    console.warn('从sessionStorage读取失败:', e)
  }

  // 从localStorage获取
  try {
    const stored = localStorage.getItem(EARLY_CAPTURE_KEY)
    if (stored) {
      return JSON.parse(stored)
    }
  } catch (e) {
    console.warn('从localStorage读取失败:', e)
  }

  return null
}

export default function Home() {
  const user = useAppStore((state) => state.user)
  const token = useAppStore((state) => state.token)

  const [url, setUrl] = useState(window.location.href)
  const [urlHistory, setUrlHistory] = useState<string[]>([])

  useMount(() => {
    // 记录URL变化历史
    const initialUrl = window.location.href
    console.log('🏠 组件挂载时URL:', initialUrl)
    setUrl(initialUrl)
    setUrlHistory(prev => [...prev, `挂载: ${initialUrl}`])

    // 监听URL变化
    const originalPushState = window.history.pushState
    const originalReplaceState = window.history.replaceState

    window.history.pushState = function(...args: any[]) {
      originalPushState.apply(window.history, args)
      const newUrl = window.location.href
      console.log('🔄 pushState变化:', newUrl)
      setUrl(newUrl)
      setUrlHistory(prev => [...prev, `pushState: ${newUrl}`])
    }

    window.history.replaceState = function(...args: any[]) {
      originalReplaceState.apply(window.history, args)
      const newUrl = window.location.href
      console.log('🔄 replaceState变化:', newUrl)
      setUrl(newUrl)
      setUrlHistory(prev => [...prev, `replaceState: ${newUrl}`])
    }

    // 监听popstate事件
    const handlePopState = () => {
      const newUrl = window.location.href
      console.log('🔄 popstate变化:', newUrl)
      setUrl(newUrl)
      setUrlHistory(prev => [...prev, `popstate: ${newUrl}`])
    }

    window.addEventListener('popstate', handlePopState)

    // 检查是否有授权回调参数
    handleAuthCallback()

    // 清理函数
    return () => {
      window.history.pushState = originalPushState
      window.history.replaceState = originalReplaceState
      window.removeEventListener('popstate', handlePopState)
    }
  })

  // 检查用户是否已登录
  const isLoggedIn = () => {
    return token && user && user.id
  }

  // 处理授权回调
  const handleAuthCallback = async () => {
    // 优先使用超早期捕获的参数
    let ultraEarly: any = null
    let earlyCaptured: any = null

    // 尝试获取超早期捕获的参数
    if (typeof window !== 'undefined' && (window as any).getUltraEarlyParams) {
      ultraEarly = (window as any).getUltraEarlyParams()
    }

    // 备用：使用之前的早期捕获
    if (!ultraEarly) {
      earlyCaptured = getEarlyCapturedParams()
    }

    const capturedParams: any = ultraEarly || earlyCaptured

    let healthCode: string | null = null
    let code: string | null = null
    let state: string | null = null

    if (capturedParams) {
      healthCode = capturedParams.healthCode || null
      code = capturedParams.code || null
      state = capturedParams.state || null
      console.log('✅ 使用捕获的参数:', capturedParams)
      console.log('📍 捕获方法:', capturedParams.captureMethod)
    } else {
      // 如果所有捕获都失败，尝试从当前URL解析
      const urlParams = new URLSearchParams(window.location.search || window.location.hash.split('?')[1] || '')
      healthCode = urlParams.get('healthCode')
      code = urlParams.get('code')
      state = urlParams.get('state')
      console.log('⚠️ 使用当前URL解析参数:', { healthCode, code, state })
    }

    // 调试信息
    console.log('🔍 当前URL:', window.location.href)
    console.log('📋 最终参数:', { healthCode, code, state })

    showToast({
      title: `healthCode: ${healthCode || '无'}, code: ${code || '无'}`,
      icon: 'none',
      duration: 3000
    })

    if (healthCode && !code) {
      // 第一步：获得健康卡授权码，现在需要获取微信授权码
      const currentUrl = window.location.href
      const redirectUri = encodeURIComponent(currentUrl)
      const appId = process.env.TARO_APP_JKZJ_APPID

      const getWeChatCodeUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&state=${healthCode}&hashMode=1#wechat_redirect`
      window.location.href = getWeChatCodeUrl
      return
    }

    if (code && state) {
      // 第二步：获得微信授权码，使用state中的healthCode进行登录
      try {
        await performHealthCardLogin(code, state)

        // 登录成功后，清除授权参数并检查是否有待执行的操作
        sessionStorage.removeItem('authParams')
        const pendingAction = sessionStorage.getItem('pendingAction')
        sessionStorage.removeItem('pendingAction')

        if (pendingAction === 'me') {
          // 跳转到个人中心
          switchTab(RoutePath.Me)
        }
        // 如果是查询功能，页面会自动刷新，用户可以重新点击
      } catch (error) {
        console.error('授权登录失败:', error)
        sessionStorage.removeItem('authParams')
        sessionStorage.removeItem('pendingAction')
      }
    }
  }

  // 执行健康卡登录流程
  const performHealthCardLogin = async (mpCode: string, healthCode: string) => {
    try {
      showLoading({ title: '登录中...' })

      // 步骤3：获取健康卡信息
      const healthCardInfo = await getHealthCardInfo({
        mpCode,
        healthCode
      })

      // 步骤4：执行登录
      const loginResult = await healthCardLogin({
        username: healthCardInfo.name,
        telephone: healthCardInfo.phone1,
        idCard: healthCardInfo.idNumber
      })

      hideLoading()

      // 登录成功，保存token并初始化用户信息
      localStorage.setItem('token', loginResult)

      // 使用store的setState方法更新token
      useAppStore.setState({ token: loginResult })

      // 初始化用户信息
      const { initUser } = useAppStore.getState()
      await initUser()

      showToast({
        title: '登录成功',
        icon: 'success'
      })
    } catch (error: any) {
      hideLoading()

      // 处理登录失败的情况
      if (error.message && error.message.includes('登录失败，未匹配到该患者建档信息')) {
        showToast({
          title: error.message,
          icon: 'error',
          duration: 3000
        })
      } else {
        showToast({
          title: '登录失败，请重试',
          icon: 'error'
        })
      }
      throw error
    }
  }

  // 启动微信授权流程
  const startWeChatAuth = () => {
    const currentUrl = window.location.href
    const redirectUri = encodeURIComponent(currentUrl)
    const hospitalId = '40384'

    // 步骤1：获取电子健康卡授权码
    const getHealthCodeUrl = `https://h5-health.tengmed.com/h5/tencent/open/card/regist?hospitalId=${hospitalId}&redirect_uri=${redirectUri}&hashMode=1`
    showToast({
      title: `getHealthCodeUrl: ${getHealthCodeUrl}`,
      icon: 'none'
    })
    // 直接跳转到健康卡授权，后续流程在回调中处理
    setTimeout(() => {
      window.location.href = getHealthCodeUrl
    }, 1000)
  }

  // 处理需要登录的操作
  const handleActionWithAuth = async (action: () => void) => {
    if (isLoggedIn()) {
      // 已登录，直接执行操作
      action()
    } else {
      // 未登录，保存操作并启动授权流程
      sessionStorage.setItem('pendingAction', 'true')
      startWeChatAuth()
    }
  }

  // 查询功能按钮点击处理
  function onEntryClick(entry: EntryItem) {
    handleActionWithAuth(() => {
      navigateTo(entry.path)
    })
  }

  // 获取当前保存的参数用于调试显示
  const ultraEarly = typeof window !== 'undefined' && (window as any).getUltraEarlyParams ? (window as any).getUltraEarlyParams() : null
  const earlyCaptured = getEarlyCapturedParams()
  const savedParams = sessionStorage.getItem('authParams')
  const authParams = savedParams ? JSON.parse(savedParams) : null

  return (
    <View className='home'>
      <View style={{ padding: '10px', fontSize: '12px', backgroundColor: '#f0f0f0', maxHeight: '300px', overflow: 'auto' }}>
        <StyledText>🔍 当前URL: {url}</StyledText>
        <br />

        {ultraEarly ? (
          <>
            <div style={{ color: 'green', fontWeight: 'bold' }}>
              <StyledText>🚀 超早期捕获成功!</StyledText>
            </div>
            <br />
            <StyledText>healthCode: {ultraEarly.healthCode || '无'}</StyledText>
            <br />
            <StyledText>code: {ultraEarly.code || '无'}</StyledText>
            <br />
            <StyledText>state: {ultraEarly.state || '无'}</StyledText>
            <br />
            <StyledText>原始URL: {ultraEarly.originalUrl || '无'}</StyledText>
            <br />
            <StyledText>捕获时间: {new Date(ultraEarly.timestamp).toLocaleTimeString()}</StyledText>
            <br />
            <StyledText>捕获方法: {ultraEarly.captureMethod || '未知'}</StyledText>
            <br />
          </>
        ) : earlyCaptured ? (
          <>
            <div style={{ color: 'orange' }}>
              <StyledText>⚠️ 普通早期捕获成功</StyledText>
            </div>
            <br />
            <StyledText>healthCode: {earlyCaptured.healthCode || '无'}</StyledText>
            <br />
            <StyledText>code: {earlyCaptured.code || '无'}</StyledText>
            <br />
            <StyledText>state: {earlyCaptured.state || '无'}</StyledText>
            <br />
            <StyledText>原始URL: {earlyCaptured.originalUrl || '无'}</StyledText>
            <br />
            <StyledText>捕获时间: {new Date(earlyCaptured.timestamp).toLocaleTimeString()}</StyledText>
            <br />
          </>
        ) : (
          <>
            <div style={{ color: 'red' }}>
              <StyledText>❌ 所有捕获都失败</StyledText>
            </div>
            <br />
          </>
        )}

        <StyledText>📦 SessionStorage: {savedParams ? '有数据' : '无数据'}</StyledText>
        {authParams && authParams !== earlyCaptured && (
          <>
            <br />
            <StyledText>备用参数: {JSON.stringify(authParams, null, 2)}</StyledText>
          </>
        )}

        <br />
        <StyledText>📜 URL历史 (最近5条):</StyledText>
        {urlHistory.slice(-5).map((historyItem, index) => (
          <div key={index} style={{ fontSize: '10px', marginLeft: '10px' }}>
            <StyledText>{historyItem}</StyledText>
          </div>
        ))}
      </View>
      <Image className='home-top-bg' src={indexTopBgUrl} />
      <View className='home-main'>
        <Flex className='entry-list' justify='space-between' wrap='wrap'>
          {entryList.map((entry) => (
            <Image key={entry.path} className='entry-image' src={entry.logoUrl} onClick={() => onEntryClick(entry)} />
          ))}
        </Flex>
      </View>
      <TabBar />
    </View>
  )
}
