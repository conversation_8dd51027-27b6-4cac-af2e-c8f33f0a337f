import { View, Image } from '@tarojs/components'
import { Flex } from '@taroify/core'
import TabBar from '@/components/tab-bar'
import { navigateTo } from '@/utils/router'
import { RoutePath } from '@/constants/route'
import { useMount } from 'ahooks'
import indexTopBgUrl from '@/assets/png/index-top-bg.png'
import jdxxUrl from '@/assets/svg/jdxx.svg'
import ylfwUrl from '@/assets/svg/ylfw.svg'
import jktjUrl from '@/assets/svg/jktj.svg'
import jkjyUrl from '@/assets/svg/jkjy.svg'
import ggwsUrl from '@/assets/svg/ggws.svg'

import './index.scss'

type EntryItem = {
  logoUrl: string
  path: string
}

const entryList: EntryItem[] = [
  { logoUrl: jdxxUrl, path: RoutePath.FilingInformation },
  { logoUrl: ylfwUrl, path: RoutePath.MedicalService },
  { logoUrl: jktjUrl, path: RoutePath.HealthCheck },
  { logoUrl: jkjyUrl, path: RoutePath.HealthEducation },
  { logoUrl: ggwsUrl, path: RoutePath.PublicHealth },
]


export default function Home() {



useMount(() => {

  })

function onEntryClick(entry: EntryItem) {
  const redirectUri = encodeURIComponent(window.location.href)
  const apppId = process.env.TARO_APP_JKZJ_APPID
  const hospitalId = '40384'

  const getCodeUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${apppId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&state=123&hashMode=1#wechat_redirect`
  // https://h5-health.tengmed.com/h5/tencent/open/card/regist?hospitalId=${hospitalId}&redirect_uri=${redirect_uri}&fail_redirect_uri=${fail_redirect_uri}&hashMode=1
  const getHealthCodeUrl = `https://h5-health.tengmed.com/h5/tencent/open/card/regist?hospitalId=${hospitalId}&redirect_uri=${redirectUri}}&hashMode=1`
  window.open()
  return
  navigateTo(entry.path)
}

  return (
    <View className='home'>
      <Image className='home-top-bg' src={indexTopBgUrl} />
      <View className='home-main'>
        <Flex className='entry-list' justify='space-between' wrap='wrap'>
          { entryList.map((entry) => <Image key={entry.path} className='entry-image' src={entry.logoUrl} onClick={()=>onEntryClick(entry)} />) }
        </Flex>
      </View>
      <TabBar />
    </View>
  )
}
