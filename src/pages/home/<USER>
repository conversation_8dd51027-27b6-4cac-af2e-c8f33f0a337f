import { View, Image } from '@tarojs/components'
import { Flex } from '@taroify/core'
import { showToast, showLoading, hideLoading } from '@tarojs/taro'
import TabBar from '@/components/tab-bar'
import { navigateTo, switchTab } from '@/utils/router'
import { RoutePath } from '@/constants/route'
import { useMount } from 'ahooks'
import useAppStore from '@/store'
import { getHealthCardInfo, healthCardLogin } from '@/api/user'
import indexTopBgUrl from '@/assets/png/index-top-bg.png'
import jdxxUrl from '@/assets/svg/jdxx.svg'
import ylfwUrl from '@/assets/svg/ylfw.svg'
import jktjUrl from '@/assets/svg/jktj.svg'
import jkjyUrl from '@/assets/svg/jkjy.svg'
import ggwsUrl from '@/assets/svg/ggws.svg'

import './index.scss'
import StyledText from '@/components/styled-text'
import { useState } from 'react'

type EntryItem = {
  logoUrl: string
  path: string
}

const entryList: EntryItem[] = [
  { logoUrl: jdxxUrl, path: RoutePath.FilingInformation },
  { logoUrl: ylfwUrl, path: RoutePath.MedicalService },
  { logoUrl: jktjUrl, path: RoutePath.HealthCheck },
  { logoUrl: jkjyUrl, path: RoutePath.HealthEducation },
  { logoUrl: ggwsUrl, path: RoutePath.PublicHealth }
]

// 在模块级别立即捕获URL参数，避免被路由清除
const captureInitialParams = () => {
  const currentUrl = window.location.href
  console.log('初始URL:', currentUrl)

  // 从URL中解析参数（支持hash路由）
  const urlParams = new URLSearchParams(window.location.search || window.location.hash.split('?')[1] || '')
  const healthCode = urlParams.get('healthCode')
  const code = urlParams.get('code')
  const state = urlParams.get('state')

  console.log('初始参数:', { healthCode, code, state })

  // 如果有参数，立即保存到sessionStorage
  if (healthCode || code || state) {
    const authParams = {
      healthCode,
      code,
      state,
      timestamp: Date.now(),
      originalUrl: currentUrl
    }
    sessionStorage.setItem('authParams', JSON.stringify(authParams))
    console.log('参数已保存到sessionStorage:', authParams)
  }

  return { healthCode, code, state }
}

// 立即执行参数捕获
const initialParams = captureInitialParams()

export default function Home() {
  const user = useAppStore((state) => state.user)
  const token = useAppStore((state) => state.token)

  const [url, setUrl] = useState(window.location.href)

  useMount(() => {
    // 检查是否有授权回调参数
    setUrl(window.location.href)
    handleAuthCallback()
  })

  // 检查用户是否已登录
  const isLoggedIn = () => {
    return token && user && user.id
  }

  // 处理授权回调
  const handleAuthCallback = async () => {
    // 优先从sessionStorage中读取参数
    let healthCode: string | null = null
    let code: string | null = null
    let state: string | null = null

    const savedParams = sessionStorage.getItem('authParams')
    if (savedParams) {
      const authParams = JSON.parse(savedParams)
      healthCode = authParams.healthCode
      code = authParams.code
      state = authParams.state
      console.log('从sessionStorage读取参数:', authParams)
    } else {
      // 如果sessionStorage中没有，再从URL解析
      const urlParams = new URLSearchParams(window.location.search || window.location.hash.split('?')[1] || '')
      healthCode = urlParams.get('healthCode')
      code = urlParams.get('code')
      state = urlParams.get('state')
      console.log('从URL解析参数:', { healthCode, code, state })
    }

    // 调试信息
    console.log('当前URL:', window.location.href)
    console.log('最终参数:', { healthCode, code, state })

    showToast({
      title: `healthCode: ${healthCode}, code: ${code}`,
      icon: 'none'
    })

    if (healthCode && !code) {
      // 第一步：获得健康卡授权码，现在需要获取微信授权码
      const currentUrl = window.location.href
      const redirectUri = encodeURIComponent(currentUrl)
      const appId = process.env.TARO_APP_JKZJ_APPID

      const getWeChatCodeUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&state=${healthCode}&hashMode=1#wechat_redirect`
      window.location.href = getWeChatCodeUrl
      return
    }

    if (code && state) {
      // 第二步：获得微信授权码，使用state中的healthCode进行登录
      try {
        await performHealthCardLogin(code, state)

        // 登录成功后，清除授权参数并检查是否有待执行的操作
        sessionStorage.removeItem('authParams')
        const pendingAction = sessionStorage.getItem('pendingAction')
        sessionStorage.removeItem('pendingAction')

        if (pendingAction === 'me') {
          // 跳转到个人中心
          switchTab(RoutePath.Me)
        }
        // 如果是查询功能，页面会自动刷新，用户可以重新点击
      } catch (error) {
        console.error('授权登录失败:', error)
        sessionStorage.removeItem('authParams')
        sessionStorage.removeItem('pendingAction')
      }
    }
  }

  // 执行健康卡登录流程
  const performHealthCardLogin = async (mpCode: string, healthCode: string) => {
    try {
      showLoading({ title: '登录中...' })

      // 步骤3：获取健康卡信息
      const healthCardInfo = await getHealthCardInfo({
        mpCode,
        healthCode
      })

      // 步骤4：执行登录
      const loginResult = await healthCardLogin({
        username: healthCardInfo.name,
        telephone: healthCardInfo.phone1,
        idCard: healthCardInfo.idNumber
      })

      hideLoading()

      // 登录成功，保存token并初始化用户信息
      localStorage.setItem('token', loginResult)

      // 使用store的setState方法更新token
      useAppStore.setState({ token: loginResult })

      // 初始化用户信息
      const { initUser } = useAppStore.getState()
      await initUser()

      showToast({
        title: '登录成功',
        icon: 'success'
      })
    } catch (error: any) {
      hideLoading()

      // 处理登录失败的情况
      if (error.message && error.message.includes('登录失败，未匹配到该患者建档信息')) {
        showToast({
          title: error.message,
          icon: 'error',
          duration: 3000
        })
      } else {
        showToast({
          title: '登录失败，请重试',
          icon: 'error'
        })
      }
      throw error
    }
  }

  // 启动微信授权流程
  const startWeChatAuth = () => {
    const currentUrl = window.location.href
    const redirectUri = encodeURIComponent(currentUrl)
    const hospitalId = '40384'

    // 步骤1：获取电子健康卡授权码
    const getHealthCodeUrl = `https://h5-health.tengmed.com/h5/tencent/open/card/regist?hospitalId=${hospitalId}&redirect_uri=${redirectUri}&hashMode=1`
    showToast({
      title: `getHealthCodeUrl: ${getHealthCodeUrl}`,
      icon: 'none'
    })
    // 直接跳转到健康卡授权，后续流程在回调中处理
    setTimeout(() => {
      window.location.href = getHealthCodeUrl
    }, 1000)
  }

  // 处理需要登录的操作
  const handleActionWithAuth = async (action: () => void) => {
    if (isLoggedIn()) {
      // 已登录，直接执行操作
      action()
    } else {
      // 未登录，保存操作并启动授权流程
      sessionStorage.setItem('pendingAction', 'true')
      startWeChatAuth()
    }
  }

  // 查询功能按钮点击处理
  function onEntryClick(entry: EntryItem) {
    handleActionWithAuth(() => {
      navigateTo(entry.path)
    })
  }

  return (
    <View className='home'>
      <View>
        <StyledText>{url}</StyledText>
      </View>
      <Image className='home-top-bg' src={indexTopBgUrl} />
      <View className='home-main'>
        <Flex className='entry-list' justify='space-between' wrap='wrap'>
          {entryList.map((entry) => (
            <Image key={entry.path} className='entry-image' src={entry.logoUrl} onClick={() => onEntryClick(entry)} />
          ))}
        </Flex>
      </View>
      <TabBar />
    </View>
  )
}
