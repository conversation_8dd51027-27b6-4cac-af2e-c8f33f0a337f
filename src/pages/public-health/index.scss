.public-health {
  overflow-y: hidden;
  height: 100vh;
  background-color: rgb(248, 250, 255);
  .header {
    height: 52px;
    background-color: #fff;
    margin-bottom: 8px;
    padding: 0 16px;
    .visit-img {
      width: 28px;
      height: 28px;
      display: block;
    }
  }
  .visit-detail-list {
    height: calc(100% - 164px);
    padding: 0 16px 12px;
    overflow-y: auto;
    .gap {
      display: inline-block;
      width: 8px;
      height: 1px;
    }
  }
  .follow-up-information-box {
    height: calc(100vh - 164px);
    overflow-y: auto;
  }
  .follow-up-information {
    background-color: #fff;
    padding: 16px;
    .patient-item {
      position: relative;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
      .bed-no {
        position: absolute;
        right: 0;
        top: 0;
        width: 50px;
        height: 24px;
        border-radius: 0px 8px 0px 20px;
        background-color: #e8f9f6;
        color: #21c8a5;
        font-size: 12px;
        text-align: center;
        line-height: 24px;
      }
      .level {
        text-align: center;
        width: 64px;
        height: 20px;
        border-radius: 4px;
        font-size: 12px;
        line-height: 18px;
      }
      .level-1 {
        color: #fa5151;
        background: rgba(250, 81, 81, 0.08);
        border: 1px solid #fa5151;
      }
      .level-2 {
        color: #ff8f1f;
        background: rgba(255, 143, 31, 0.08);
        border: 1px solid #ff8f1f;
      }
      .level-3 {
        color: #a55bf9;
        background: rgba(165, 91, 249, 0.08);
        border: 1px solid #a55bf9;
      }
    }
  }
  .medication-status {
    height: calc(100vh - 164px);
    overflow-y: auto;
    // background-color: #fff;
    // padding: 16px;
  }
  .medication-status-item {
    background-color: #fff;
    padding: 16px;
  }
  .basic-in-formation {
    height: calc(100vh - 164px);
    border-radius: 8px;
    overflow-y: auto;
    padding: 16px;
    background-color: #fff;
  }
  .follow-up-information-details {
    height: calc(100vh - 70px);
    overflow-y: auto;
    border-radius: 8px;
    padding: 16px;
    margin: 12px 16px;
    background-color: #fff;
  }
}
