import StyledNavbar from '@/components/styled-navbar'
import TabSelect, { Tab } from '@/components/tab-select/index'
import { Tabs } from '@taroify/core'
import { View } from '@tarojs/components'
import { SetStateAction, useState } from 'react'

import BasicInformation from './components/BasicInformation'
import FollowUpInformation from './components/FollowUpInformation'
import MedicationStatus from './components/MedicationStatus'

import './index.scss'

const tabs: any[] = [
  { text: '基本信息', key: 'BasicInformation', comp: BasicInformation },
  { text: '服药情况', key: 'MedicationStatus', comp: MedicationStatus },
  { text: '随访信息', key: 'FollowUpInformation', comp: FollowUpInformation }
]
const tabsTitle: any[] = [
  { text: '高血压管理', key: 'hypertensionMg' },
  { text: '糖尿病管理', key: 'diabetesMg' }
]

export default function PublicHealth() {
  const [selectedTab, setSelectedTab] = useState(tabs[0])
  const [tabOpened, setTabOpened] = useState(false)
  const [value, setValue] = useState(0)
  const TabComp = selectedTab.comp as unknown as typeof BasicInformation | typeof MedicationStatus | typeof FollowUpInformation
  // eslint-disable-next-line @typescript-eslint/no-shadow
  function handleChange(value: SetStateAction<number>) {
    setValue(value)
    setSelectedTab(tabs[0])
  }
  return (
    <>
      <StyledNavbar title='公共卫生' />
      <View className='public-health'>
        <Tabs value={value} onChange={handleChange} animated>
          {tabsTitle?.map((item, index) => (
            <Tabs.TabPane key={index} title={item.text}>
              <View className='line'></View>
              <TabSelect value={selectedTab} onChange={setSelectedTab} tabs={tabs} opened={tabOpened} onOpenedChange={setTabOpened} />

              <View className='visit-detail-list'>
                {selectedTab.key && item.key && <TabComp type={selectedTab.key} tabKey={item.key} />}
              </View>
            </Tabs.TabPane>
          ))}
        </Tabs>
      </View>
    </>
  )
}
