import { FollowUpInfo, getFollowerUpDetailById, getFollowUpInfo } from '@/api/public-health'
import StyledNavbar from '@/components/styled-navbar'
import { Styled333Text, Styled999Text } from '@/components/styled-text'
import { useRouteSearch } from '@/utils'
import { Flex } from '@taroify/core'
import { View } from '@tarojs/components'
import { useRequest } from 'ahooks'
import './index.scss'

const apiInfo = {
  hypertensionMg: getFollowUpInfo,
  diabetesMg: getFollowerUpDetailById
}
const FollowUpInformationDetails = () => {
  const search = useRouteSearch()
  const { data: res } = useRequest<FollowUpInfo, any[]>(() => apiInfo[search.tabKey](search.id)())
  return (
    <>
      <StyledNavbar title='随访详情' />
      <View className='public-health'>
        <View className='follow-up-information-details box-shadow border-radius-8'>
          <Styled333Text bold font-size={16} className='mb-12'>
            体征
          </Styled333Text>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>血压（mmHg）</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.bp || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>体重（kg）</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.weight || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>

          {search.tabKey === 'diabetesMg' ? (
            <>
              <Flex justify='space-between' className='mb-12'>
                <Flex.Item>
                  <Styled999Text>体质指数(BMI)(kg/m2)</Styled999Text>
                </Flex.Item>
                <Flex.Item>
                  <Styled333Text>{res?.bmi || '无'}</Styled333Text>
                </Flex.Item>
              </Flex>
              <Flex justify='space-between' className='mb-12'>
                <Flex.Item>
                  <Styled999Text>足背动脉搏动</Styled999Text>
                </Flex.Item>
                <Flex.Item>
                  <Styled333Text>{res?.pulsation || '无'}</Styled333Text>
                </Flex.Item>
              </Flex>
            </>
          ) : (
            <>
              <Flex justify='space-between' className='mb-12'>
                <Flex.Item>
                  <Styled999Text>心率（次/分钟）</Styled999Text>
                </Flex.Item>
                <Flex.Item>
                  <Styled333Text>{res?.heartRate || '无'}</Styled333Text>
                </Flex.Item>
              </Flex>
            </>
          )}

          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>其他</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.otherSigns || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <View className='line mb-12'></View>
          <Styled333Text bold font-size={16} className='mb-12'>
            生活方式指导
          </Styled333Text>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>日吸烟量（支）</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.smokeCount || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>日饮酒两（两）</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.drinkCount || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>运动</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>
                {res?.trainTimesWeek || 0}次/周 {res?.trainMinute || 0}分钟/次
              </Styled333Text>
            </Flex.Item>
          </Flex>
          {search.tabKey === 'diabetesMg' ? (
            <Flex justify='space-between' className='mb-12'>
              <Flex.Item>
                <Styled999Text>主食(克/天)</Styled999Text>
              </Flex.Item>
              <Flex.Item>
                <Styled333Text>{res?.food || '无'}</Styled333Text>
              </Flex.Item>
            </Flex>
          ) : (
            <Flex justify='space-between' className='mb-12'>
              <Flex.Item>
                <Styled999Text>摄盐情况（咸淡）</Styled999Text>
              </Flex.Item>
              <Flex.Item>
                <Styled333Text>{res?.targetSalt || '无'}</Styled333Text>
              </Flex.Item>
            </Flex>
          )}

          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>心理调整</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.psychologyChange || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>遵医行为</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.obeyDoctor || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <View className='line mb-12'></View>
          {search.tabKey === 'diabetesMg' ? (
            <>
              <Styled333Text bold font-size={16} className='mb-12'>
                辅助检查
              </Styled333Text>
              <Flex justify='space-between' className='mb-12'>
                <Flex.Item>
                  <Styled999Text>空腹血糖值(mol/L)</Styled999Text>
                </Flex.Item>
                <Flex.Item>
                  <Styled333Text>{res?.fbs || '无'}</Styled333Text>
                </Flex.Item>
              </Flex>
              <Flex justify='space-between' className='mb-12'>
                <Flex.Item>
                  <Styled999Text>其他检查</Styled999Text>
                </Flex.Item>
                <Flex.Item>
                  <Styled333Text>{res?.otherCheck || '无'}</Styled333Text>
                </Flex.Item>
              </Flex>
              <View className='line mb-12'></View>
            </>
          ) : null}
          <Styled333Text bold font-size={16} className='mb-12'>
            用药情况
          </Styled333Text>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>药物名称1</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.insulinTypeOne || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>用法用量</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>
                每日{res?.frequencyOne}次 每次{res?.doseOne}
              </Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>药物名称2</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.insulinTypeTwo || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>用法用量</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>
                每日{res?.frequencyTwo}次 每次{res?.doseTwo}
              </Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>药物名称3</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.doseThree || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>用法用量</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>
                每日{res?.frequencyThree}次 每次{res?.doseThree}
              </Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>其他药物</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.doseFour || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>用法用量</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>
                每日{res?.frequencyFour}次 每次{res?.doseFour}
              </Styled333Text>
            </Flex.Item>
          </Flex>
          <View className='line mb-12'></View>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>服药依从性</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.medicine || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>药物不良反应</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.adverseReactions || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>此次随访分类</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.visitType || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>下次随访日期</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.nextDate || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
          <Flex justify='space-between' className='mb-12'>
            <Flex.Item>
              <Styled999Text>随访医生签名</Styled999Text>
            </Flex.Item>
            <Flex.Item>
              <Styled333Text>{res?.visitDoctor || '无'}</Styled333Text>
            </Flex.Item>
          </Flex>
        </View>
      </View>
    </>
  )
}
export default FollowUpInformationDetails
