import { Styled333Text, Styled999Text } from '@/components/styled-text'
import { Flex } from '@taroify/core'
import { FC } from 'react'

// type Props = {
//   data: ItemType[]
// }
export type ItemType = {
  label: string
  value: string
}

const Item: FC<ItemType> = (props) => {
  return (
    <Flex justify='space-between' className='mb-12'>
      <Flex.Item>
        <Styled999Text>{props.label}</Styled999Text>
      </Flex.Item>
      <Flex.Item>
        <Styled333Text>{props.value}</Styled333Text>
      </Flex.Item>
    </Flex>
  )
}
export default Item
