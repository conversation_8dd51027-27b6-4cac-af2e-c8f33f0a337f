import { getDiabetesMgBasicInfo, getHypertensionMgBasicInfo } from '@/api/public-health'
import StyledList from '@/components/styled-list'
import { getUser } from '@/store'
import { useRequest } from 'ahooks'
import { FC } from 'react'
import Item from './Item'

const apiInfo = {
  hypertensionMg: getHypertensionMgBasicInfo,
  diabetesMg: getDiabetesMgBasicInfo
}
type BasicInformationProps = {
  type: string
  tabKey: string
}
const BasicInformation: FC<BasicInformationProps> = (props) => {
  const userInfo = getUser()
  const { data, loading } = useRequest(() => apiInfo[props.tabKey](userInfo.empiId)(), {
    refreshDeps: [props.tabKey, props.type]
  })

  return (
    <>
      <StyledList {...props} loading={loading} className='basic-in-formation' data={data} comp={Item} />
    </>
  )
}

export default BasicInformation
