import { getDiabetesFollowerUp, getFollowUpList } from '@/api/public-health'
import StyledList from '@/components/styled-list'
import { RoutePath } from '@/constants/route'
import { getUser } from '@/store'
import { stringifyURL } from '@/utils'
import { navigateTo } from '@/utils/router'
import { Button } from '@taroify/core'
import { View } from '@tarojs/components'
import { useRequest } from 'ahooks'
import { FC } from 'react'
import Item from './Item'
// import './index.scss'

const apiInfo = {
  hypertensionMg: getFollowUpList,
  diabetesMg: getDiabetesFollowerUp
}
type FollowUpInformationProps = {
  type: string
  tabKey: string
}
const textLable = {
  visitDate: '随访日期',
  visitDoctor: '随访医生',
  symptoms: '症状',
  visitWay: '随访方式',
  nextDate: '下次随访日期'
}

function ItemComp({ list, visitId, tabKey }) {
  const onClick = () => {
    navigateTo(`${RoutePath.FollowUpInformationDetails}?${stringifyURL({ id: visitId, tabKey })}`)
  }
  return (
    <>
      <View className='follow-up-information box-shadow border-radius-8 mt-12'>
        {list && list.length > 0 ? list?.map((item, index) => <Item key={index} {...item}></Item>) : null}
        <View style={{ textAlign: 'right' }} onClick={onClick}>
          <Button key={1} size='small' variant='outlined' color='primary'>
            查看详情
          </Button>
        </View>
      </View>
    </>
  )
}

const FollowUpInformation: FC<FollowUpInformationProps> = (props) => {
  const userInfo = getUser()
  const { data, loading } = useRequest<any[], any>(() => apiInfo[props.tabKey](userInfo.empiId)(), {
    refreshDeps: [props.tabKey, props.type]
  })
  const result =
    data &&
    data?.map((item) => {
      const list = Object.keys(textLable).map((key) => {
        return {
          label: textLable[key],
          value: item[key]
        }
      })

      return {
        visitId: item.visitId,
        list,
        tabKey: props.tabKey
      }
    })

  return <StyledList className='follow-up-information-box' loading={loading} data={result} comp={ItemComp} />
}
export default FollowUpInformation
