import { getDiabetesMedication, getUsedrug } from '@/api/public-health'
import StyledList from '@/components/styled-list'
import { Styled333Text, Styled999Text } from '@/components/styled-text'
import { getUser } from '@/store'
import { Flex } from '@taroify/core'
import { View } from '@tarojs/components'
import { useRequest } from 'ahooks'
import { FC } from 'react'
import Item from './Item'

const apiInfo = {
  hypertensionMg: getUsedrug,
  diabetesMg: getDiabetesMedication
}
type MedicationStatusProps = {
  type: string
  tabKey: string
}
const textLable = {
  medicineType: '药品分类',
  medicineName: '药物名称',
  createDate: '开药时间'
}

function ItemComp({ list, medicineDosage, medicineFrequency, days, medicineTotalDosage }) {
  return (
    <>
      <View className='box-shadow border-radius-8 mb-12 medication-status-item'>
        {list && list.length > 0 ? list?.map((item, index) => <Item key={index} {...item}></Item>) : null}
        <View className='line'></View>
        <Flex wrap='wrap' justify='space-between'>
          <Flex.Item span={10}>
            <Styled999Text small inline>
              用量：
            </Styled999Text>
            <Styled333Text small inline>
              {medicineDosage}
            </Styled333Text>
          </Flex.Item>
          <Flex.Item span={8}>
            <Styled999Text small inline>
              次数/日：
            </Styled999Text>
            <Styled333Text small inline>
              {medicineFrequency}
            </Styled333Text>
          </Flex.Item>
          <Flex.Item span={6}>
            <Styled999Text small inline>
              天数：
            </Styled999Text>
            <Styled333Text small inline>
              {days}
            </Styled333Text>
          </Flex.Item>
          <Flex.Item span={12}>
            <Styled999Text small inline>
              总量：
            </Styled999Text>
            <Styled333Text small inline>
              {medicineTotalDosage}
            </Styled333Text>
          </Flex.Item>
        </Flex>
      </View>
    </>
  )
}

const MedicationStatus: FC<MedicationStatusProps> = (props) => {
  const userInfo = getUser()
  const { data, loading } = useRequest(() => apiInfo[props.tabKey](userInfo.empiId)(), {
    refreshDeps: [props.tabKey, props.type]
  })
  const result =
    data &&
    data?.map((item) => {
      const list = Object.keys(textLable).map((key) => {
        return {
          label: textLable[key],
          value: item[key]
        }
      })

      return {
        ...item,
        list
      }
    })

  return <StyledList className='medication-status' loading={loading} data={result} comp={ItemComp} />
}
export default MedicationStatus
