import { useRequest } from 'ahooks'
import StyledNavbar from '@/components/styled-navbar'
import { useRouteSearch } from "@/utils"
import { getHealthEducationDetail } from '@/api/health-education'
import './detail.scss'

// row 行 column 列 行和列 交叉的部分是cell单元格

export default function HealthEducationDetail() {
  const search = useRouteSearch()
  const { data } = useRequest(getHealthEducationDetail, { defaultParams: [{ id: search.id }] })
  return <>
    <StyledNavbar title={data?.educationTitle || '健康教育'} />
    {/* eslint-disable-next-line react/no-danger */}
    <div dangerouslySetInnerHTML={{ __html: data?.mainContent || '' }} className='health-education-detail'></div>
  </>
}
