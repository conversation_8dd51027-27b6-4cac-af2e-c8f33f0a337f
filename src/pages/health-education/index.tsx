import { useRequest } from 'ahooks'
import { View } from '@tarojs/components'
import { Button } from '@taroify/core'
import { Styled333Text } from '@/components/styled-text'
import StyledNavbar from '@/components/styled-navbar'
import StyledList from '@/components/styled-list'
import StyledCellList from '@/components/styled-cell-list'
import { getHealthEducationList } from '@/api/health-education'
import { navigateTo } from '@/utils/router'
import { RoutePath } from '@/constants/route'
import './index.scss'

const fields = [
  { label: '宣教时间', field: 'createTime' },
  { label: '宣教单位', field: 'orgName' },
]

function Item({ educationTitle, id, ...rest }) {
  return <View className='item'>
    <Styled333Text large className='mb-12' bold>{educationTitle}</Styled333Text>
    <StyledCellList fields={fields} data={rest} px={0} />
    <View className='btn' onClick={() => navigateTo(RoutePath.HealthEducationDetail, { id })}>
      <Button size='small' variant='outlined' color='primary'>查看详情</Button>
    </View>
  </View>
}

export default function HealthEducation() {
  const { data, loading } = useRequest(getHealthEducationList)
  return <>
    <StyledNavbar title='健康教育' />
    <View className='health-education-list'>
      <StyledList data={data} loading={loading} comp={Item} />
    </View>
  </>
}
