import { useState } from 'react'
import { useRequest } from 'ahooks'
import cls from 'classnames'
import { getPastHistory } from '@/api/user'
import { View, Image } from '@tarojs/components'
import { Flex } from '@taroify/core'
import TabBar from '@/components/tab-bar'
import useAppStore from '@/store'
import  StyledText, { Styled333Text, StyledWhiteText, StyledLightWhiteText } from '@/components/styled-text'
import StyledCellList from '@/components/styled-cell-list'
import StyledSpace from '@/components/styled-space'
import GenderIcon from '@/components/gender-icon'
import meUserUrl from '@/assets/svg/me-user.svg'
import ywgmsUrl from '@/assets/svg/ywgms.svg'
import ywgmsActiveUrl from '@/assets/svg/ywgms-active.svg'
import jwsUrl from '@/assets/svg/jws.svg'
import jwsActiveUrl from '@/assets/svg/jws-active.svg'
import jzsUrl from '@/assets/svg/jzs.svg'
import jzsActiveUrl from '@/assets/svg/jzs-active.svg'
import sssUrl from '@/assets/svg/sss.svg'
import sssActiveUrl from '@/assets/svg/sss-active.svg'
import './index.scss'


const tabs = [
  {
    url: ywgmsUrl, activeUrl: ywgmsActiveUrl, text: '药物过敏史', key: 'key1',
    fields: [
      { label: '药物过敏史', field: 'drugAllergyHistory' },
      { label: '暴露史', field: 'exposureHistory' },
    ]
  },
  {
    url: jwsUrl, activeUrl: jwsActiveUrl, text: '既往史', key: 'key2',
    fields: [
      { label: '疾病史(含外伤)', field: 'diseaseHistory' },
      { label: '既往史确诊日期', field: 'diagnosisDate' }
    ]
  },
  {
    url: jzsUrl, activeUrl: jzsActiveUrl, text: '家族史', key: 'key3',
    fields: [
      { label: '父亲', field: 'fatherName' },
      { label: '母亲', field: 'motherName' },
      { label: '子女', field: 'brothersSistersName' },
      { label: '遗传病史', field: 'geneticHistory' },
      { label: '残疾情况', field: 'disability' },
    ]
  },
  {
    url: sssUrl, activeUrl: sssActiveUrl, text: '手术史', key: 'key4',
    fields: [
      { label: '疾病史', field: 'operationDiseaseHistory' },
      { label: '手术', field: 'operationName' },
      { label: '手术日期', field: 'operationDate' },
      { label: '输血', field: 'transfusion' },
      { label: '输血日期', field: 'transfusionDate' },
    ]
  },
]

function Content() {
  const user = useAppStore(state => state.user)
  const [active, setActive] = useState(tabs[0])
  const {data} = useRequest(() => Object.keys(user).length > 0 ? getPastHistory({
    empiId: user.empiId
  }) : Promise.resolve([]), {
    refreshDeps: [user]
  })
  return <View className='content'>
    <Flex className='tab-wrapper'>
      { tabs.map((tab, index) => <Flex key={index} onClick={() => setActive(tab)} style={{flex: 1}} align='center' justify='center' direction='column'>
        <Image src={active.key === tab.key ? tab.activeUrl : tab.url} />
        <StyledText className={cls('mt-12', { active: active.key === tab.key })}>{tab.text}</StyledText>
      </Flex>) }
    </Flex>
    <View>
      <StyledCellList data={data} fields={active.fields} />
    </View>
  </View>
}

export default function Index() {
  const data = useAppStore(state => state.user)

  return (
    <View className='me'>
      <Flex className='header' align='center'>
        <View className='header-logo'>
          {data?.name?.slice(data?.name?.length - 2, data?.name?.length)}
        </View>
        <Flex direction='column'>
          <Styled333Text fontSize={18} bold>{data?.name}</Styled333Text>
          <StyledSpace gapX={16}>
            <Styled333Text large>{data?.age ? `${data?.age}`: ''}</Styled333Text>
            <GenderIcon sexCode={data?.sex} />
          </StyledSpace>
        </Flex>
      </Flex>
      <Flex className='id-card' justify='space-between' align='center'>
        <Flex direction='column'>
          <StyledSpace gapX={16}>
            <StyledLightWhiteText>档案编号</StyledLightWhiteText>
            <StyledWhiteText>{data.phrId}</StyledWhiteText>
          </StyledSpace>
          <StyledSpace gapX={16}>
            <StyledLightWhiteText>联系方式</StyledLightWhiteText>
            <StyledWhiteText>{data.mobileNumber}</StyledWhiteText>
          </StyledSpace>
          <StyledSpace gapX={16}>
            <StyledLightWhiteText>身份证号</StyledLightWhiteText>
            <StyledWhiteText>{data.idCard}</StyledWhiteText>
          </StyledSpace>
        </Flex>
        <Image src={meUserUrl} className='user-logo' />
      </Flex>
      <View className='me-main'>
        <Content />
      </View>
      <TabBar />
    </View>
  )
}
