.me {
  height: 100%;
  width: 100%;
  background-image: url('@/assets/svg/me-bg.svg');
  background-repeat: no-repeat;
  background-size: cover;
  .header {
    padding: 36px 16px 24px;
    .header-logo {
      width: 64px;
      height: 64px;
      border-radius: 100%;
      font-size: 22px;
      line-height: 64px;
      text-align: center;
      color: #fff;
      background: var(--primary-color);
      margin-right: 16px;
    }
  }
  .id-card {
    background: linear-gradient(270deg, #81EEE6 0%, #589DF7 60%);
    padding: 12px 16px;
    border-radius: 8px 8px 0px 0px;
    margin: 0 auto;
    width: calc(100% - 32px);
    .user-logo {
      width: 40px;
      height: 40px;
    }
  }
  .me-main {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    background: linear-gradient(180deg, #F9FBFF 50%, #FFFFFF 100%);
    box-shadow: 0px -5px 10px 0px rgba(0, 0, 0, 0.05);
    height: calc(100% - 296px);
    padding: 16px;
    position: relative;
    overflow: hidden;
    .content {
      background-color: #fff;
      border-radius: 8px;
      height: 100%;
      padding: 16px;
      .tab-wrapper {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border-color);
        .active {
          color: var(--primary-color);
        }
      }
    }
  }
}
