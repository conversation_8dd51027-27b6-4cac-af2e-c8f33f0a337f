.visit-detail-wrapper {
  height: calc(100% - 44px);
  overflow-y: hidden;
  background-color: rgb(248, 250, 255);
  .header {
    height: 52px;
    background-color: #fff;
    margin-bottom: 8px;
    padding: 0 16px;
    .visit-img {
      width: 28px;
      height: 28px;
      display: block;
    }
  }
  .visit-detail-list {
    background: #fff;
    height: calc(100% - 164px);
    padding: 16px;
    overflow-y: auto;
    .gap {
      display: inline-block;
      width: 8px;
      height: 1px;
    }
  }
  .hospital-order-dropdown {
    margin-top: 0;
    margin-left: -16px;
    margin-right: -16px;
    margin-bottom: 12px;
  }
  .taroify-list {
    .medical-record-item {
      margin: 0 12px;
      padding: 12px 0;
      background: #fafafa;
      border-bottom: 1px solid #eee;
      .left {
        width: calc(100% - 20px);
      }
      .right {
        width: 16px;
        height: 16px;
      }
    }
    .divider {
      color: #eee;
      border-color: #eee;
      padding: 0;
      margin: 12px 0;
    }
  }
  .taroify-steps {
    .taroify-steps__items {
      .taroify-step--active {
        .taroify-step__label {
          .one {
            taro-view-core {
              color: #21c8a5 !important;
            }
          }
          .two {
            color: #333333 !important;
          }
        }
      }
    }
  }
  .HospitalOrder {
    position: relative;
    padding: 12px;
    font-family: HarmonyOS Sans SC;
    font-weight: normal;
    line-height: 18px;
    .image {
      position: absolute;
      right: -10px;
      top: -8px;
    }
    .medicOrdersTypeCode {
      width: 20px;
      height: 38px;
      padding: 6px 4px;
      transform: rotate(1800deg);
      transform-origin: center center;
      font-family: HarmonyOS Sans SC;
      font-size: 12px;
      font-weight: 500;
      line-height: 14px;
      letter-spacing: 0px;
      position: absolute;
      left: 0;
      top: 50%;
      // transform: translateY(-50%);
      margin-top: -27px;
    }
    .medicOrdersTypeCode0 {
      background: #e5ebff;
      color: #567fff;
    }
    .medicOrdersTypeCode1 {
      background: #e1f5ff;
      color: #22c3f0;
    }
  }
  .OutpatientPrescriptionImage {
    position: absolute;
    right: 0;
    top: 2px;
  }
}
