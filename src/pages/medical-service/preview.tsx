import { useState, useEffect } from 'react'
import { useRouteSearch } from '@/utils'
import { View, WebView } from '@tarojs/components'
import StyledNavbar from '@/components/styled-navbar'
import { getElectronicCaseHistoryDocDetailUrl } from '@/api/patient-detail'
import './preview.scss'

export default function Preview() {
  const [src, setSrc] = useState('')
  const search = useRouteSearch()
  const { type, pkElectronicCaseHistoryDoc, title } = search
  const onLoad = () => {
    const iframe = document.querySelector('iframe')?.contentDocument
    if (iframe) {
      let viewport = iframe.querySelector("meta[name=viewport]") as HTMLMetaElement
      if (!viewport){
          viewport=iframe.createElement('meta') as HTMLMetaElement
          viewport.name = "viewport"
          iframe.getElementsByTagName('head')[0].appendChild(viewport)
      }
      const content = 'width=device-width, initial-scale=1.0, user-scalable=yes'
      viewport.setAttribute('content', content);
    }

    if (iframe) {
      const iframeBody = iframe.body
      const scaleX = document.documentElement.clientWidth / (iframeBody.scrollWidth + 24)
      iframeBody.style.margin = '8px'
      iframeBody.style.width = `${document.documentElement.clientWidth}px`
      iframeBody.style.height = 'calc(100vh - 2.2rem)'
      iframeBody.style.transform = `scale(${scaleX})`
      iframeBody.style.transformOrigin = '0 0'
    }
  }
  useEffect(() => {
    if (type === 'outpatient' || type === 'inpatient') {
      setSrc(getElectronicCaseHistoryDocDetailUrl(pkElectronicCaseHistoryDoc))
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  return <View className='preview'>
    <StyledNavbar title={title || '预览'} />
    {
      src && <WebView id='view' src={src} onLoad={onLoad} />
    }
  </View>
}
