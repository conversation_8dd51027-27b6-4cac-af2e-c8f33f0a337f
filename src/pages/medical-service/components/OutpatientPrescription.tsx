import { getOutpatientPrescription, OutpatientPrescriptionItem } from '@/api/patient-detail'
import StyledCollapsePanel from '@/components/styled-collapse-panel'
import StyledList from '@/components/styled-list'
import { Styled333Text, Styled999Text } from '@/components/styled-text'
import { Divider, Empty, Flex } from '@taroify/core'
import { Image, View } from '@tarojs/components'
import { pxTransform } from '@tarojs/taro'
import { useRequest } from 'ahooks'
import PUrl from '../../../assets/svg/P.svg'
import PCCUrl from '../../../assets/svg/PCC.svg'
import PCZUrl from '../../../assets/svg/PCZ.svg'

const imageStyle = { height: pxTransform(20)}
const imageUrlInfo={
  P:PUrl,
  PCC:PCCUrl,
  PCZ:PCZUrl
}
function Item({ diagnosisDept,diagnoseDiseaseName,prescriptionType,visitDate,hospitalOrg,diagnosisDoctor,medicationVOS}: OutpatientPrescriptionItem) {
  return <>
    <StyledCollapsePanel
      title={<>
        <Styled333Text large ellipsis={1} className='mb-4 pr-16'>[{diagnosisDept || ''}]{diagnoseDiseaseName || ''}</Styled333Text>
        <Styled999Text small ellipsis={1}>{prescriptionType || ''}<View className='gap'></View>{visitDate || ''}</Styled999Text>
        <Styled999Text small ellipsis={1}>{hospitalOrg || ''}<View className='gap'></View>医生：{diagnosisDoctor || ''}</Styled999Text>
      </>}
      bodyRender={(visible) => visible ? <><View className='p-12 relative'>
        {
          prescriptionType === '中药处方' ?  <View>
          {
           medicationVOS && medicationVOS.length > 0 ?
           medicationVOS?.map((item,idx)=>{
              return <View key={idx} className='mb-12'>
              <Flex justify='start' wrap='wrap'>
                {
                  item?.medicationVOS?.map((value,index)=>{
                    return  <Styled333Text className='mr-16 mb-4' key={index} inline>{value?.medicOrdersItem} {value?.drugDosageUnit}</Styled333Text>
                  })
                }
                </Flex>
                <Image className='OutpatientPrescriptionImage' src={PCCUrl} style={imageStyle} />
                <Divider style={{ color: "#EEEEEE", borderColor: "#EEEEEE", padding: "0" ,margin: "12px 0" }} />
                <Flex>
                  <Styled333Text small inline className='mr-16'>{item?.dosage ? `共${item?.dosage}剂` : ''}</Styled333Text>
                  <Styled999Text small inline>用法：</Styled999Text>
                  <Styled333Text small inline className='mr-16'>{item?.tcmUsageMethod}</Styled333Text>
                  <Styled999Text small inline>一次用量：</Styled999Text>
                  <Styled333Text small inline>{item?.yl ? `${item?.yl}ml` : ''}</Styled333Text>
                </Flex>
              </View>
            }):
            <Empty>
              <Empty.Image />
              <Empty.Description>暂无数据</Empty.Description>
            </Empty>
          }
          </View>: <View>
            {
                medicationVOS && medicationVOS.length > 0 ?
              medicationVOS?.map((item,index)=>{
                return <View key={index} style={{position: 'relative',width:'100%'}}>
                <Image className='OutpatientPrescriptionImage' src={imageUrlInfo[item?.drugType]} style={imageStyle} />
                <Styled333Text large ellipsis={3} className='mb-4 pr-16'>{item?.medicOrdersItem || ''}</Styled333Text>
                <Styled999Text small ellipsis={1}>{item?.drugDosageUnit || ''}<View className='gap'></View>{item?.drugSpecification || ''}</Styled999Text>
                <Styled999Text small ellipsis={1}>
                <Styled999Text small inline>用法：</Styled999Text>
                  <Styled333Text small inline className='mr-16'>{item?.tcmUsageMethod}</Styled333Text>
                  <Styled999Text small inline>频次：</Styled999Text>
                  <Styled333Text small inline className='mr-16'>{item?.drugUseFrequency}</Styled333Text>
                  <Styled999Text small inline>天数：</Styled999Text>
                  <Styled333Text small inline>{item?.prescriptionEffectiveDays ? `${item?.prescriptionEffectiveDays}天` : ''}</Styled333Text>
                </Styled999Text>
                <Divider style={{ color: "#EEEEEE", borderColor: "#EEEEEE", padding: "0" ,margin: "12px 0" }} />
              </View>
              }):
              <Empty>
                <Empty.Image />
                <Empty.Description>暂无数据</Empty.Description>
              </Empty>
            }
            </View>
        }
        </View></> : null}
    />
  </>
}

export default function OutpatientPrescription({ empiId }) {
  const { data, loading } = useRequest(getOutpatientPrescription, {
    defaultParams: [{ empiId }],
  })
  return <>
    <Styled333Text large bold className='mb-16'>门诊处方</Styled333Text>
    <StyledList data={data} loading={loading} comp={Item} />
  </>
}

