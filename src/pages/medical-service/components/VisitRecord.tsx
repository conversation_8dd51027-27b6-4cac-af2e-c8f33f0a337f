import { useRequest } from 'ahooks'
import { Image } from '@tarojs/components'
import { Styled333Text, Styled666Text, Styled999Text } from '@/components/styled-text'
import { Empty, List, Steps, Flex, Loading } from '@taroify/core'
import StyledSpace from '@/components/styled-space'
import cls from 'classnames'
import stepsIconUrlActive from '@/assets/svg/steps-icon-active.svg'
import stepsIconUrl from '@/assets/svg/steps-icon.svg'
import { TabCompProps } from './TabSelect'
// eslint-disable-next-line import/first
import { getDiagnosisInfoList } from '@/api/patient-detail'

function StyledSteps({ className, loading, data }) {
  return <List className={cls('styled-steps', className)} loading={loading} >
    {
      loading ?
        loading && <Flex align='center' justify='center' style={{ marginTop: '4rem' }}><Loading /></Flex> :
        data && data.length > 0 ?
          <Steps defaultValue={0} direction='vertical'>
            {
              data.map((item, idx) => <Steps.Step icon={<Image src={idx === 0 ? stepsIconUrlActive : stepsIconUrl} />} key={idx}>
                <StyledSpace className='one' gapX={12}>
                  {
                    item.visitTypeCode === '3' ?
                      <Styled999Text small>{item?.admitHospitalTime || ''} ~ {item?.leaveTime || '至今'}</Styled999Text> :
                      <Styled999Text small>{item.visitDate}</Styled999Text>
                  }
                  <Styled999Text small>{item?.hospitalOrg}</Styled999Text>
                  <Styled999Text small>{item?.diagnosisDoctor}</Styled999Text>
                </StyledSpace>
                <Styled666Text className='two pt-4' ellipsis={1}>【{item?.visitType || ''}】[{item?.diagnosisDept}]{item?.diagnoseDiseaseName}</Styled666Text>
              </Steps.Step>)
            }
        </Steps> :
          <Empty>
            <Empty.Image />
            <Empty.Description>暂无数据</Empty.Description>
      </Empty>
    }
  </List>
}


export default function VisitRecord({ empiId }: TabCompProps) {
  const { data, loading } = useRequest(() => getDiagnosisInfoList({empiId}), {
    refreshDeps: [empiId]
  })
  return <>
    <Styled333Text bold large>就诊记录</Styled333Text>
    <StyledSteps className='pt-8' data={data} loading={loading} />
  </>
}
