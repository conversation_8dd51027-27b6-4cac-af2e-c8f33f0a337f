import { ExamineResultReport, getExamineResultReportList } from '@/api/patient-detail'
import StyledCollapsePanel from '@/components/styled-collapse-panel'
import StyledList from '@/components/styled-list'
import { Styled333Text, Styled999Text } from '@/components/styled-text'
import { Divider, Flex } from '@taroify/core'
import { View } from '@tarojs/components'
import { useRequest } from 'ahooks'

function Item({ examineReportOrg, examineReportDate, examineReportItem, reptResultObjectiveOpinion,reptResultSubjectiveOpinion,examineReportDept,examineReportDoctor}: ExamineResultReport) {
  return <>
    <StyledCollapsePanel
      title={<>
        <Styled333Text large ellipsis={1} className='mb-4 pr-16'>{examineReportItem}</Styled333Text>
        <Styled999Text small ellipsis={1}>{examineReportOrg}<View className='gap'></View>{examineReportDate}</Styled999Text>
      </>}
      bodyRender={(visible) => visible ? <><View className='p-12'>
        <Styled999Text>检查所见：</Styled999Text>
        <Styled333Text>{reptResultObjectiveOpinion}</Styled333Text>
        <Styled999Text className='mt-12'>检查意见：</Styled999Text>
        <Styled333Text>{reptResultSubjectiveOpinion||''}</Styled333Text>
        <Divider className='divider' />
        <Flex>
          <Flex.Item span={12}>
            <Styled999Text inline>检查科室：</Styled999Text>
            <Styled333Text inline>{examineReportDept}</Styled333Text>
          </Flex.Item>
          <Flex.Item span={12}>
            <Styled999Text inline>报告医生：</Styled999Text>
            <Styled333Text inline>{examineReportDoctor}</Styled333Text>
          </Flex.Item>
        </Flex>
        </View>
      </> : null}
    />
  </>
}

export default function CheckReport({ empiId }) {
  const { data, loading } = useRequest(getExamineResultReportList, {
    defaultParams: [{ empiId }],
  })
  return <>
    <Styled333Text large bold className='mb-16'>检查报告</Styled333Text>
    <StyledList data={data} loading={loading} comp={Item} />
  </>
}
