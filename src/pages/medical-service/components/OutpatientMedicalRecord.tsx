import { useCallback, useRef } from 'react'
import { useRequest } from 'ahooks'
import { Flex } from '@taroify/core'
import { Image } from '@tarojs/components'
import { navigateTo } from '@/utils/router'
import { Styled333Text, Styled999Text } from '@/components/styled-text'
import StyledSpace from '@/components/styled-space'
import StyledList from '@/components/styled-list'
import { RoutePath } from '@/constants/route'
import { stringifyURL } from '@/utils'
import StyledCollapsePanel from '@/components/styled-collapse-panel'
import { getClinicMedicalRecordList, getElectronicCaseHistoryDocList, ClinicMedicalRecord, ElectronicCaseHistoryDoc } from '@/api/patient-detail'
import arrowUrl from '@/assets/svg/arrow.svg'

function ItemBody({ docName, docCreatorName, docCreateTime, pkElectronicCaseHistoryDoc }: ElectronicCaseHistoryDoc) {
  const onClick = () => {
    navigateTo(`${RoutePath.PreviewDetail}?${stringifyURL({ type: 'inpatient', pkElectronicCaseHistoryDoc, title: docName })}`)
  }
  return <Flex justify='space-between' align='center' className='medical-record-item' onClick={onClick}>
    <Flex className='left' direction='column'>
      <Styled333Text className='mb-4'>{docName}</Styled333Text>
      <StyledSpace gapX={12}>
        <Flex>
          <Styled999Text small>记录人：</Styled999Text>
          <Styled333Text small>{docCreatorName}</Styled333Text>
        </Flex>
        <Styled333Text small>{docCreateTime}</Styled333Text>
      </StyledSpace>
    </Flex>
    <Image src={arrowUrl} className='right' />
  </Flex>
}

function Item({ diagnoseDiseaseName, hospitalOrg, pkVisitInfo, visitDate, diagnosisDept }: ClinicMedicalRecord) {
  const onceRef = useRef(false)
  const { data, loading, runAsync } = useRequest(() => getElectronicCaseHistoryDocList({ pkVisitInfo }), {
    manual: true
  })
  const bodyRender = useCallback((isExpanded: boolean, refreshContentHeight: () => void) => {
    if (isExpanded && !onceRef.current) {
      onceRef.current = true
      runAsync().finally(() => {
        refreshContentHeight()
      })
    }
    if (!isExpanded) {
      return null
    }
    return <StyledList comp={ItemBody} loading={loading} data={data}></StyledList>
  }, [data, loading])
  return <>
    <StyledCollapsePanel
      title={<>
        <Styled333Text large ellipsis={1} className='mb-4 pr-16'>{diagnosisDept ? `[${diagnosisDept}]` : ''}{diagnoseDiseaseName}</Styled333Text>
        <StyledSpace gapX={12}>
          <Styled999Text small>{hospitalOrg}</Styled999Text>
          <Styled999Text small>{visitDate}</Styled999Text>
        </StyledSpace>
      </>}
      bodyRender={bodyRender}
      async
    />
  </>
}

export default function OutpatientMedicalRecord({ empiId }) {
  const { data, loading } = useRequest(getClinicMedicalRecordList, {
    defaultParams: [{ empiId }],
  })
  return <>
    <Styled333Text large bold className='mb-16'>门诊病历</Styled333Text>
    <StyledList data={data} loading={loading} comp={Item} />
  </>
}
