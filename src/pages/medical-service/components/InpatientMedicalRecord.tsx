import { useRequest } from 'ahooks'
import { useCallback, useRef } from 'react'
import { Flex } from '@taroify/core'
import { View, Image } from '@tarojs/components'
import arrowUrl from '@/assets/svg/arrow.svg'
import StyledCollapsePanel from '@/components/styled-collapse-panel'
import StyledList from '@/components/styled-list'
import StyledSpace from '@/components/styled-space'
import { Styled333Text, Styled999Text } from '@/components/styled-text'
import { RoutePath } from '@/constants/route'
import { stringifyURL } from '@/utils'
import { navigateTo } from '@/utils/router'
import { ElectronicCaseHistoryDoc, getElectronicCaseHistoryDocList, getInpatientMedicalRecordList, InpatientMedicalRecord } from '@/api/patient-detail'
import { formatDate } from '@/utils'

function ItemBody({ docName, docCreatorName, docCreateTime, pkElectronicCaseHistoryDoc }: ElectronicCaseHistoryDoc) {
  const onClick = () => {
    navigateTo(`${RoutePath.PreviewDetail}?${stringifyURL({ type: 'inpatient', pkElectronicCaseHistoryDoc, title: docName })}`)
  }
  return <Flex justify='space-between' align='center' className='medical-record-item' onClick={onClick}>
    <Flex className='left' direction='column'>
      <Styled333Text className='mb-4'>{docName}</Styled333Text>
      <StyledSpace gapX={12}>
        <Flex>
          <Styled999Text small>记录人：</Styled999Text>
          <Styled333Text small>{docCreatorName}</Styled333Text>
        </Flex>
        <Styled333Text small>{docCreateTime}</Styled333Text>
      </StyledSpace>
    </Flex>
    {/* taroify bug */}
    <Image src={arrowUrl} className='right' />
  </Flex>
}

function Item({ admitHospitalTime, leaveTime, diagnosisInfo, deptName, hospitalOrg,  pkInpatientVisitInfo }: InpatientMedicalRecord) {
  const onceRef = useRef(false)
  const { data, loading, runAsync } = useRequest(() => getElectronicCaseHistoryDocList({ pkVisitInfo: pkInpatientVisitInfo }), {
    manual: true
  })
  // const timeStr = useMemo(() => `${admitHospitalTime}至${leaveTime || '今'}`, [admitHospitalTime, leaveTime])
  const bodyRender = useCallback((isExpanded: boolean, refreshContentHeight: () => void) => {
    if (isExpanded && !onceRef.current) {
      onceRef.current = true
      runAsync().finally(() => {
        refreshContentHeight()
      })
    }
    if (!isExpanded) {
      return null
    }
    return <StyledList comp={ItemBody} loading={loading} data={data}></StyledList>
  }, [data, loading])
  return <>
    <StyledCollapsePanel
      title={<>
        <Styled333Text large ellipsis={1} className='mb-4 pr-16'>{deptName ? `[${deptName}]` : ''}{diagnosisInfo}</Styled333Text>
        <Styled999Text small ellipsis={1}>{hospitalOrg}<View className='gap'></View>{admitHospitalTime} ~ {leaveTime || '至今'}</Styled999Text>
      </>}
      bodyRender={bodyRender}
      async
    />
  </>
}

export default function OutpatientMedicalRecord({ empiId }) {
  const { data, loading } = useRequest(getInpatientMedicalRecordList, {
    defaultParams: [{ empiId }],
    onSuccess(res) {
      res?.forEach(item => {
        item.admitHospitalTime = formatDate(item.admitHospitalTime)
        item.leaveTime = formatDate(item.leaveTime)
      })
      return res
    }
  })
  return <>
    <Styled333Text large bold className='mb-16'>住院病历</Styled333Text>
    <StyledList data={data} loading={loading} comp={Item} />
  </>
}
