import { useState, useRef } from 'react'
import { useRequest, useUnmount, useMount } from 'ahooks'
import { Divider, DropdownMenu, Empty } from '@taroify/core'
import { Image, View } from '@tarojs/components'
import { pxTransform } from '@tarojs/taro'
import StyledCollapsePanel from '@/components/styled-collapse-panel'
import StyledList from '@/components/styled-list'
import StyledText, { Styled333Text, Styled999Text } from '@/components/styled-text'
import StyledSpace from '@/components/styled-space'
import { getInPateintMedicalOrders, InPateintMedicalOrders } from '@/api/patient-detail'
import { formatDate } from '@/utils'

import PUrl from '@/assets/svg/P.svg'
import PCCUrl from '@/assets/svg/PCC.svg'
import PCZUrl from '@/assets/svg/PCZ.svg'


const imageStyle = { height: pxTransform(20)}
const imageUrlInfo={
  西药:PUrl,
  中药:PCCUrl,
  中成药:PCZUrl
}
const medicOrdersTypeCodeInfo={
  0:'临时',
  1:'长期',
}
const medicOrdersTypeCodeClass={
  0:'medicOrdersTypeCode medicOrdersTypeCode0',
  1:'medicOrdersTypeCode medicOrdersTypeCode1',
}
function filterDate(date:string){
  return date?.substring(0, date?.indexOf(' '))?.replace(/-/g, '.')
}
function Item({ deptName,hospitalOrg,medicalOrdersVOList,diagnosisInfo,medicOrdersdoctor,admitHospitalTime}: InPateintMedicalOrders) {
  return <>
    <StyledCollapsePanel
      title={<>
        <Styled333Text large ellipsis={1} className='mb-4 pr-16'>[{deptName || ''}]{diagnosisInfo || ''}</Styled333Text>
        <Styled999Text small ellipsis={1}>{hospitalOrg || ''}<View className='gap'></View>医生：{medicOrdersdoctor || ''}<View className='gap'></View>入院时间：{admitHospitalTime || ''}</Styled999Text>
      </>}
      bodyRender={(visible) =>visible ? <><View className='HospitalOrder'>
        <View>
            {
               medicalOrdersVOList && medicalOrdersVOList.length > 0 ?
              medicalOrdersVOList?.map((item,index)=>{
                return <View key={index} style={{position: 'relative',width:'100%'}}>
               <View className='pl-32'>
                <Image className='image' src={imageUrlInfo[item?.medicOrdersItemType]} style={imageStyle} />
                  <View className={medicOrdersTypeCodeClass[item?.medicOrdersTypeCode]}>{medicOrdersTypeCodeInfo[item?.medicOrdersTypeCode] || ''}</View>
                  <StyledText className={medicOrdersTypeCodeClass[item?.medicOrdersTypeCode]}>{medicOrdersTypeCodeInfo[item?.medicOrdersTypeCode] || ''}</StyledText>
                  <Styled333Text className='mb-4' small>
                  {
                    item.medicOrdersTypeCode === '1' ?
                    `${item?.medicOrdersExcuteDate} ~ ${item?.medicOrdersStopDate || '至今'}` :
                    item.medicOrdersExcuteDate
                  }
                  </Styled333Text>
                  <Styled333Text>{item?.medicOrdersItem}</Styled333Text>
                  <StyledSpace gapX={12}>
                    <Styled333Text inline>{item?.drugUsed}</Styled333Text>
                    <Styled333Text inline>{item?.drugSpec}</Styled333Text>
                    <View>
                      <Styled999Text small inline>用法：</Styled999Text>
                      <Styled333Text small inline>{item?.drugUsage}</Styled333Text>
                    </View>
                    <View>
                      <Styled999Text small inline>频次：</Styled999Text>
                      <Styled333Text small inline>{item?.drugUseDrequency}</Styled333Text>
                    </View>
                  </StyledSpace>
               </View>
                <Divider style={{ color: "#EEEEEE", borderColor: "#EEEEEE", padding: "0" ,margin: "12px 0" }} />
              </View>
              }):
              <Empty>
                <Empty.Image />
                <Empty.Description>暂无数据</Empty.Description>
              </Empty>
            }
            </View>
        </View></> : null}
    />
  </>
}

export default function HospitalOrder({ empiId }) {
  const openRef = useRef()
  const open2Ref = useRef()
  const fnRef = useRef((e) => {
    if (openRef.current || open2Ref.current) {
      e?.preventDefault()
    }
  })
  const [medicOrderTypeCode, setMedicOrderTypeCode] = useState()
  const [medicOrderItemType, setMedicOrderItemType] = useState()
  const { data, loading } = useRequest(()=>getInPateintMedicalOrders({empiId,medicOrderTypeCode,medicOrderItemType}), {
    refreshDeps: [empiId,medicOrderTypeCode,medicOrderItemType],
    onSuccess(res) {
      res?.forEach(item => {
        item.medicalOrdersVOList.forEach(subItem => {
          subItem.medicOrdersExcuteDate = formatDate(subItem.medicOrdersExcuteDate)
          subItem.medicOrdersStopDate = formatDate(subItem.medicOrdersStopDate)
        })
      })
    }
  })
  useMount(() => {
    window.addEventListener('touchmove', fnRef.current, { passive: false })
  })
  useUnmount(() => {
    window.removeEventListener('touchmove', fnRef.current)
  })
  const onOpenChange = (open) => {
    openRef.current = open
  }
  const onOpenChange2 = (open) => {
    open2Ref.current = open
  }
  function handleChange(value){
    setMedicOrderTypeCode(value)
  }
  function HandleMedicOrderItemTypeChange(value){
    setMedicOrderItemType(value)
  }
  return <>
    <Styled333Text large bold className='mb-16'>住院医嘱</Styled333Text>
    <DropdownMenu className='hospital-order-dropdown'>
      {/* todo  */}
      <DropdownMenu.Item value={medicOrderTypeCode} onChange={handleChange} onOpenChange={onOpenChange}>
        <DropdownMenu.Option  value=''>全部医嘱</DropdownMenu.Option>
        <DropdownMenu.Option value='1'>长期医嘱</DropdownMenu.Option>
        <DropdownMenu.Option value='0'>临时医嘱</DropdownMenu.Option>
      </DropdownMenu.Item>
      <DropdownMenu.Item value={medicOrderItemType} onChange={HandleMedicOrderItemTypeChange} onOpenChange={onOpenChange2}>
        <DropdownMenu.Option value=''>全部</DropdownMenu.Option>
        <DropdownMenu.Option value='西药'>西药</DropdownMenu.Option>
        <DropdownMenu.Option value='中药'>中药</DropdownMenu.Option>
      </DropdownMenu.Item>
    </DropdownMenu>
    <StyledList data={data} loading={loading} comp={Item} />
  </>
}

