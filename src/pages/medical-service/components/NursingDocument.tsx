import { useRequest } from 'ahooks'
import { View } from '@tarojs/components'
import { Styled333Text, Styled999Text } from '@/components/styled-text'
import StyledList from '@/components/styled-list'
import StyledCollapsePanel from '@/components/styled-collapse-panel'
import { getInpatientMedicalRecordList, InpatientMedicalRecord } from '@/api/patient-detail'
import { formatDate } from '@/utils'

function Item({ admitHospitalTime, leaveTime, diagnosisInfo, deptName, hospitalOrg }: InpatientMedicalRecord) {
  return <>
    <StyledCollapsePanel
      title={<>
        <Styled333Text large ellipsis={1} className='mb-4 pr-16'>[{deptName || '--'}]{diagnosisInfo || '--'}</Styled333Text>
        <Styled999Text small ellipsis={1}>{hospitalOrg}<View className='gap'></View>{admitHospitalTime}~{leaveTime || '至今'}</Styled999Text>
      </>}
      bodyRender={() => null}
    />
  </>
}

export default function NursingDocument({ empiId }) {
  const { data, loading } = useRequest(getInpatientMedicalRecordList, {
    defaultParams: [{ empiId }],
    onSuccess(res) {
      res?.forEach(item => {
        item.admitHospitalTime = formatDate(item.admitHospitalTime)
        item.leaveTime = formatDate(item.leaveTime)
      })
      return res
    }
  })
  return <>
    <Styled333Text large bold className='mb-16'>护理文书</Styled333Text>
    <StyledList data={data} loading={loading} comp={Item} />
  </>
}
