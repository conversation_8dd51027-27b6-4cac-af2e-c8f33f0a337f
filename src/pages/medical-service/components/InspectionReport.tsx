import { getPateintTestreport, PateintTestreport } from '@/api/patient-detail'
import StyledCollapsePanel from '@/components/styled-collapse-panel'
import StyledList from '@/components/styled-list'
import { Styled333Text, Styled999Text } from '@/components/styled-text'
import { Divider, Empty, Flex } from '@taroify/core'
import { View } from '@tarojs/components'
import { useRequest } from 'ahooks'
// import './InspectionReport.scss'

const criticalValueFlagInfo={
  正常:'',
  高:<span style={{color:'#FA5151'}}>↑</span>,
  低:<span style={{color:'#FA5151',transform: 'rotate(180deg)',display: 'inline-block'}}>↑</span>
}
function Item({ testResultDetailList,labReportItem,hospitalOrg,labReportDate,labReportDept,labReportDoctor}: PateintTestreport) {
  return <>
    <StyledCollapsePanel
      title={<>
        <Styled333Text large ellipsis={1} className='mb-4 pr-16'>{labReportItem || ''}</Styled333Text>
        <Styled999Text small ellipsis={1}>{hospitalOrg}<View className='gap'></View>{labReportDate}</Styled999Text>
      </>}
      bodyRender={(visible) => visible ? <><View className='p-12'>
        {
            testResultDetailList && testResultDetailList.length > 0 ?
            testResultDetailList?.map((item,idx)=>{
            return  <View key={idx} className='mb-12'>
              <Flex justify='space-between'>
                <Flex.Item >
                  <Styled333Text fontSize={14}>{item?.labTestIndexesDesc||'-'}</Styled333Text>
                </Flex.Item>
                <Flex.Item >
                  <Styled333Text fontSize={14}>{criticalValueFlagInfo[item?.criticalValueFlag]} {item?.labQuantitativeResult}</Styled333Text>
                </Flex.Item>
              </Flex>
              <Flex justify='space-between'>
                <Flex.Item >
                  <Styled999Text small>参考值：{item?.referenceValueRange||'-'}</Styled999Text>
                </Flex.Item>
                <Flex.Item >
                  <Styled999Text small>{item?.qttativeResultMeasureUnit||'-'}</Styled999Text>
                </Flex.Item>
              </Flex>
            </View>
          }):
          <Empty>
            <Empty.Image />
            <Empty.Description>暂无数据</Empty.Description>
          </Empty>
        }
        <Divider style={{ color: "#EEEEEE", borderColor: "#EEEEEE", padding: "0" ,margin: "12px 0" }} />
        <Flex>
          <Flex.Item span={12}>
            <Styled999Text inline>检查科室：</Styled999Text>
            <Styled333Text inline>{labReportDept}</Styled333Text>
          </Flex.Item>
          <Flex.Item span={12}>
            <Styled999Text inline>报告医生：</Styled999Text>
            <Styled333Text inline>{labReportDoctor}</Styled333Text>
          </Flex.Item>
        </Flex>
        </View></> : null}
    />
  </>
}

export default function InspectionReport({ empiId }) {
  const { data, loading } = useRequest(getPateintTestreport, {
    defaultParams: [{ empiId }],
  })
  return <>
    <Styled333Text large bold className='mb-16'>检验报告</Styled333Text>
    <StyledList data={data} loading={loading} comp={Item} />
  </>
}
