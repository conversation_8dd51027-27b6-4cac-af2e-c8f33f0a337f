import { useState } from 'react'
import imageUrl from '@/assets/png/visit-header.png'
import GenderIcon from '@/components/gender-icon'
import StyledNavbar from '@/components/styled-navbar'
import StyledSpace from '@/components/styled-space'
import { Styled333Text } from '@/components/styled-text'
import TabSelect, { Tab } from '@/components/tab-select/index'
import { Image, View } from '@tarojs/components'
import { useUser } from '@/store'
import CheckReport from './components/CheckReport'
import HospitalOrder from './components/HospitalOrder'
import InpatientMedicalRecord from './components/InpatientMedicalRecord'
import InspectionReport from './components/InspectionReport'
// import NursingDocument from './components/NursingDocument'
import OutpatientMedicalRecord from './components/OutpatientMedicalRecord'
import OutpatientPrescription from './components/OutpatientPrescription'
// import SurgicalAnesthesia from './components/SurgicalAnesthesia'
import VisitRecord from './components/VisitRecord'

import './index.scss'


const tabs: Tab[] = [
  { text: '就诊记录', key: 'VisitRecord', comp: VisitRecord },
  { text: '检查报告', key: 'CheckReport', comp: CheckReport },
  { text: '检验报告', key: 'InspectionReport', comp: InspectionReport },
  { text: '门诊处方', key: 'OutpatientPrescription', comp: OutpatientPrescription },
  { text: '住院医嘱', key: 'HospitalOrder', comp: HospitalOrder },
  { text: '门诊病历', key: 'OutpatientMedicalRecord', comp: OutpatientMedicalRecord },
  { text: '住院病历', key: 'InpatientMedicalRecord', comp: InpatientMedicalRecord },
  // { text: '护理文书', key: 'NursingDocument', comp: NursingDocument }
  // { text: '手术麻醉', key: 'SurgicalAnesthesia', comp: SurgicalAnesthesia },
]

export default function Operation() {
  const { name, age, sex, idCard, empiId } = useUser()
  const [selectedTab, setSelectedTab] = useState(tabs[0])
  const [tabOpened, setTabOpened] = useState(false)
  const TabComp = selectedTab.comp

  return (
    <>
      <StyledNavbar title='医疗服务' />
      <View className='visit-detail-wrapper'>
        <StyledSpace gapX={16} className='header'>
          <Image src={imageUrl} className='visit-img' />
          <Styled333Text>{name}</Styled333Text>
          <GenderIcon sexCode={sex} />
          <Styled333Text>{age}</Styled333Text>
          <Styled333Text>{idCard}</Styled333Text>
        </StyledSpace>
        <TabSelect value={selectedTab} onChange={setSelectedTab} tabs={tabs} opened={tabOpened} onOpenedChange={setTabOpened} />
        <View className='visit-detail-list'>
          {
            empiId && <TabComp empiId={empiId} />
          }
        </View>
      </View>
    </>
  )
}
