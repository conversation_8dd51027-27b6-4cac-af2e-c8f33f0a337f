import { useState, useEffect } from 'react'
import { useMount } from 'ahooks'
import { useRouteSearch } from '@/utils'
import { View, WebView } from '@tarojs/components'
import StyledNavbar from '@/components/styled-navbar'
import { getCheckDetail } from '@/api/health-check'
import './detail.scss'


export default function Preview() {
  // const [src, setSrc] = useState('')
  const search = useRouteSearch()
  const { pkPhysicalExamReport } = search

  return <View className='preview'>
    <StyledNavbar title='体检报告详情' />
    <WebView src={`/pdf-view/index.html?${getCheckDetail(pkPhysicalExamReport)}`} />
  </View>
}
