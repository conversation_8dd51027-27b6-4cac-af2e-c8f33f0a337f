import { useState, useRef } from 'react'
import { useRequest, useMount, useUnmount } from 'ahooks'
import { View } from '@tarojs/components'
import { Button, Flex, DropdownMenu } from '@taroify/core'
import StyledNavbar from '@/components/styled-navbar'
import { Styled333Text } from '@/components/styled-text'
import StyledList from '@/components/styled-list'
import StyledCellList from '@/components/styled-cell-list'
import { getCheckList, getLifeCycleInfo, LifeCycleInfo } from '@/api/health-check'
import { navigateTo } from '@/utils/router'
import { RoutePath } from '@/constants/route'
import { useUser } from '@/store'
import './index.scss'

const fields = [
  { label: '体检日期', field: 'visitDate' },
  { label: '体检机构', field: 'hospitalOrg' },
  { label: '体检套餐', field: 'packageName' },
  { label: '异常项个数', field: 'abnormalNum' },
]

function Item({ reportUrl, pkPhysicalExamReport, ...rest }) {
  return <View className='item'>
    <StyledCellList fields={fields} data={rest} px={0} />
    {
      reportUrl &&  <View className='btn'>
        <Button size='small' variant='outlined' color='primary' onClick={() => navigateTo(RoutePath.HealthCheckDetail, { pkPhysicalExamReport: pkPhysicalExamReport })}>查看详情</Button>
      </View>
    }
  </View>
}

export default function HealthCheck() {
  const openRef = useRef()
  const fnRef = useRef((e) => {
    if (openRef.current) {
      e?.preventDefault()
    }
  })
  const user = useUser()
  const [value, setValue] = useState<LifeCycleInfo>()
  const { data: lifeCycle } = useRequest(getLifeCycleInfo, {
    onSuccess(res) {
      setValue(res?.[0])
    }
  })
  const { data, loading } = useRequest(() => value ? getCheckList({ lifeCycleBeginYear: value.beginYear, lifeCycleEndYear: value.endYear, empiId: user.empiId }) : Promise.resolve([]), {
    ready: !!user.empiId,
    refreshDeps: [value]
  })

  const onOpenChange = (open) => {
    openRef.current = open
  }

  useMount(() => {
    window.addEventListener('touchmove', fnRef.current, { passive: false })
  })
  useUnmount(() => {
    window.removeEventListener('touchmove', fnRef.current)
  })

  return <>
    <StyledNavbar title='健康体检' />
    <View className='health-check-list'>
      <Flex align='center' justify='space-between' className='mb-8'>
        <Styled333Text>体检列表</Styled333Text>
          <DropdownMenu>
            <DropdownMenu.Item value={value} onChange={setValue} onOpenChange={onOpenChange}>
              {
                (lifeCycle || []).map((item) => <DropdownMenu.Option key={item.value} value={item}>{item.name}</DropdownMenu.Option>)
              }
            </DropdownMenu.Item>
        </DropdownMenu>
      </Flex>
      <StyledList data={data} loading={loading} comp={Item} />
    </View>
  </>
}
