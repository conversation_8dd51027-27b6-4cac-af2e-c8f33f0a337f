import { sendVerificationCode } from '@/api/user'

import imgUrl from '@/assets/png/login-top-right.png'
import BlankGap from '@/components/blank-gap'
import StyledText, { Styled333Text } from '@/components/styled-text'
import useAppStore from '@/store'
import { Button, Flex, Input } from '@taroify/core'
import { Image, View } from '@tarojs/components'
import { showToast } from '@tarojs/taro'
import { useInterval } from 'ahooks'
import { useState } from 'react'

import './index.scss'

export default function Index() {
  const [login] = useAppStore((state) => [state.login])
  const [cardId, setCardId] = useState('')
  const [name, setName] = useState('')
  const [telephone, setTelephone] = useState('')
  const [verifyCode, setVerifyCode] = useState('')
  const [second, setSecond] = useState(0)
  const [interval, setInterval] = useState<number | undefined>()
  const [isSendVerify, setIsSendVerify] = useState(false)
  const [loading, setLoading] = useState(false)

  useInterval(() => {
    if (second > 0) {
      setSecond(second - 1)
    } else {
      setInterval(undefined)
    }
  }, interval)

  const onSendCode = () => {
    if (!telephone) return
    if (second) return
    sendVerificationCode({ telephone }).then(() => {
      showToast({
        title: '发送成功',
        icon: 'success'
      })
      setInterval(1000)
      setSecond(120)
      setIsSendVerify(true)
    }).catch(() => {
      setIsSendVerify(false)
    })
  }

  const onSubmit = async () => {
    setLoading(true)
    await login({
      idCardNo: cardId,
      name,
      telephone,
      verificationCode: verifyCode
    })
    setLoading(false)
  }

  return (
    <View className='index'>
      <View className='header'>
        <View className='left'>
          <Styled333Text fontSize={22} bold>
            欢迎登录！
          </Styled333Text>
          <StyledText color='#7D869C'>欢迎使用居民健康档案平台</StyledText>
        </View>
        <Image src={imgUrl} className='right'></Image>
      </View>
      <View className='main'>
        <Styled333Text>身份证号</Styled333Text>
        <Input value={cardId} onChange={(e) => setCardId(e.detail.value)} placeholder='请输入您的身份证号' />

        <Styled333Text>姓名</Styled333Text>
        <Input value={name} onChange={(e) => setName(e.detail.value)} placeholder='请输入您的姓名' />

        <Styled333Text>手机号</Styled333Text>
        <Input value={telephone} onChange={(e) => setTelephone(e.detail.value)} placeholder='请输入手机号' />

        <Styled333Text>验证码</Styled333Text>
        <Flex className='verify-code-wrapper' align='center'>
          <Input value={verifyCode} onChange={(e) => setVerifyCode(e.detail.value)} placeholder='请输入验证码' />
          <Flex className='verify-code' align='center'>
            <Flex className='verify-code-left'></Flex>
            <Flex className='verify-code-text' style={{ color: telephone ? 'var(--primary-color)' : '#666' }} onClick={onSendCode}>
              {second === 0 ? '发送验证码' : `${second}秒后重发`}
            </Flex>
          </Flex>
        </Flex>

      <BlankGap height={24} />
      <Button block onClick={onSubmit} loading={loading} disabled={!(isSendVerify && cardId && name && telephone && verifyCode)} color='primary'>登录</Button>
      <BlankGap />
    </View>
  </View>
  )
}
