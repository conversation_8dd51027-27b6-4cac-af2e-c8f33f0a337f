.index {
  width: 100%;
  height: 100%;
  .header {
    background-image: url('@/assets/svg/index-bg.svg');
    background-size: 100% 292px;
    width: 100%;
    height: 292px;
    position: relative;
    .left {
      position: absolute;
      left: 24px;
      bottom: 140px;
    }
    .right {
      position: absolute;
      right: 16px;
      bottom: 100px;
      width: 152px;
      height: 121px;
    }
  }
  .main {
    position: relative;
    margin-top: -110px;
    padding: 24px;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    background: linear-gradient(180deg, #FFFFFF 50%, #F7F9FA 102%);
    box-shadow: 0px -4px 14px 0px rgba(16, 68, 190, 0.05);
    height: calc(100% - 172px);
    overflow-y: auto;
    .taroify-input {
      margin-top: 12px;
      margin-bottom: 20px;
      height: 48px;
      background: #F8F8FA;
      border-radius: 4px;
      padding: 16px;
      .taroify-native-input {
        line-height: 16px;
        height: 16px;
      }
    }
    .verify-code-wrapper {
      .taroify-input {
        flex: 1;
        border-radius: 4px 0 0 4px;
      }
      .verify-code {
        margin-top: 12px;
        margin-bottom: 20px;
        background-color: #F8F8FA;
        &-left {
          height: 16px;
          background-color: #EEE;
          margin-right: 8px;
          vertical-align: 12px;
          width: 1px;
        }
        &-text {
          text-align: center;
          width: 96px;
          height: 48px;
          line-height: 48px;

          background-color: #F8F8FA;
          color: var(--primary-color);
          font-size: 14px;
        }
      }
    }
    .feedback--invalid {
      color: var(--danger-color);
      font-size: 14px;
      margin-top: 4px;
    }
  }
}
