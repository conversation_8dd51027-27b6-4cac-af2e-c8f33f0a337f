import { useState, createContext } from 'react'
import { View } from '@tarojs/components'
import { Tabs } from '@taroify/core'
import StyledNavbar from '@/components/styled-navbar'
import PersonalRecord from './PersonalRecord'
import FamilyRecord from './FamilyRecord'
import './index.scss'

export const Context = createContext({
  cacheKey: ''
})

export default function FilingInformation() {
  const [value, setValue] = useState('personalRecord')
  const [contentValue] = useState({
    cacheKey: new Date().toString()
  })
  return <Context.Provider value={contentValue}>
    <StyledNavbar title='建档信息' />
    <View className='filing-information'>
     <Tabs animated swipeable value={value} onChange={setValue}>
      <Tabs.TabPane title='个人档案' value='personalRecord'>
        {
          value === 'personalRecord' && <PersonalRecord />
        }
      </Tabs.TabPane>
      <Tabs.TabPane title='家庭档案' value='familyRecord'>
        {
          value === 'familyRecord' && <FamilyRecord />
        }
      </Tabs.TabPane>
     </Tabs>
    </View>
  </Context.Provider>
}
