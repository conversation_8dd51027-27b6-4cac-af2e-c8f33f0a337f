import { useContext } from 'react'
import { useRequest } from 'ahooks'
import { View, Image } from '@tarojs/components'
import StyledCellList from '@/components/styled-cell-list'
import { getFamilyRecordInfo } from '@/api/record-info'
import { useUser } from '@/store'
import StyledSpace from '@/components/styled-space'
import logoUrl from '@/assets/svg/record-header-right.svg'
import { Context } from './index'

const residentHealthFields = [
  { label: '户主姓名', field: 'hzxm' },
  { label: '健康档案编号', field: 'jkdabh' },
  { label: '常住类型', field: 'czlx' },
  { label: '居住地址', field: 'jzdz' },
  { label: '户籍派出所', field: 'hjpcs' },
  { label: '户属性', field: 'hsx' },
  { label: '邮政编码', field: 'yzbm' },
  { label: '现住人口数', field: 'xzrks' },
  { label: '住房总面积', field: 'zfzmj' },
  { label: '人均居住面积', field: 'rjjzmj' },
  { label: '经济状况', field: 'jjzk' },
  { label: '月人均收入', field: 'yrjsr' },
  { label: '家庭总收入', field: 'jtzsr' },
  { label: '家庭总支出', field: 'jtzzc' },
  { label: '住房性质', field: 'zfxz' },
  { label: '房东姓名', field: 'fdxm' },
  { label: '房东电话', field: 'fddh' },
  { label: '住房间数', field: 'zfjs' },
  { label: '住房采光', field: 'zfcg' },
  { label: '房屋类型', field: 'fwlx' },
  { label: '家庭住房编码', field: 'jtzfbm' },
  { label: '厨房', field: 'cf' },
  { label: '厨房排风设施', field: 'cfpfss' },
  { label: '燃料类型', field: 'rllx' },
  { label: '饮水', field: 'ys' },
  { label: '厕所', field: 'cs' },
  { label: '禽畜栏', field: 'xcl' },
  { label: '家用电器', field: 'jydq' },
  { label: '交通工具', field: 'jtgj' },
  { label: '垃圾处理', field: 'ljcl' },
  { label: '污水处理', field: 'wscl' },
  { label: '文体设备', field: 'wtsb' },
  { label: '建档医生', field: 'jdys' },
  { label: '建档日期', field: 'jdrq' },
  { label: '责任医生', field: 'zrys' },
  { label: '备注', field: 'bz' },
]

export default function FamilyRecord() {
  const { cacheKey } = useContext(Context)
  const user = useUser()
  const { data } = useRequest(() => getFamilyRecordInfo({ id: user.jtdabh }), {
    cacheKey: `${cacheKey}-familyRecordInfo`,
    staleTime: -1,
    ready: !!user.jtdabh
  })
  return <View className='content-wrapper'>
    <View className='content'>
      <View className='header family-record-header'>
      <StyledSpace>
          <View>家庭编号</View>
          <View>{user.jtdabh}</View>
        </StyledSpace>
        <Image src={logoUrl} className='logo' />
      </View>
      <View className='pt-16'>
        <StyledCellList fields={residentHealthFields} data={data} />
      </View>
    </View>
  </View>
}
