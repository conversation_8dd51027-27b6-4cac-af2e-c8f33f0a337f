.filing-information {
  height: 100%;
  .content-wrapper {
    background-color: var(--bg-color);
    overflow-y: auto;
    height: calc(100vh - 84px);
    padding: 16px 12px;
    padding-bottom: 24px;
    .content {
      background-color: #fff;
      box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
      padding-bottom: 12px;
      min-height: 100%;
      .header {
        overflow: hidden;
        height: 48px;
        padding: 0 16px;
        line-height: 48px;
        color: #F0F5FF;
        font-size: 14px;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        .logo {
          width: 4.6rem;
          height: 3.6rem;
          margin-right: -20px;
          margin-top: -12px;
        }
      }
      .personal-record-header {
        background: linear-gradient(270deg, #739DFF 0%, #4378F4 100%);
      }
      .family-record-header {
        background: linear-gradient(270deg, #4CD6CB 0%, #07B9B9 100%);
      }
      .gap-line {
        margin: 16px;
        background: #F2F4F5;
        height: 1px;
        width: calc(100% - 32px);
      }
    }
  }
}
