import { useState, useContext } from 'react'
import { useRequest } from 'ahooks'
import { View, Image } from '@tarojs/components'
import { Flex } from '@taroify/core'
import TabSelect, { Tab } from '@/components/tab-select'
import StyledCellList from '@/components/styled-cell-list'
import { getResidentHealthInfo, getPatientInfo } from '@/api/record-info'
import { useUser } from '@/store'
import logoUrl from '@/assets/svg/record-header-right.svg'
import StyledSpace from '@/components/styled-space'
import { Context } from './index'

const residentHealthFields = [
  { label: '姓名', field: 'name' },
  { label: '现住址', field: 'presentAddress' },
  { label: '户籍地址', field: 'homeAddress' },
  { label: '联系电话', field: 'mobileNumber' },
  { label: '乡镇(街道)名称', field: 'homeAddressTown' },
  { label: '村(居)委会名称', field: 'homeAddressVillage' },
]
const residentHealthFields2 = [
  { label: '建档单位', field: 'filingUnit' },
  { label: '建档人', field: 'creatorName' },
  { label: '责任医生', field: 'doctorName' },
  { label: '建档日期', field: 'creatorTime' },
]

const patientInfoField = [
  { label: '姓名', field: 'name' },
  { label: '性别', field: 'sexName' },
  { label: '出生日期', field: 'birthday' },
  { label: '身份证号', field: 'idCard' },
  { label: '本人电话', field: 'mobileNumber' },
  { label: '联系人姓名', field: 'contactName' },
  { label: '联系人电话', field: 'contactPhone' },
  { label: '常住类型', field: 'permanentAddressFlagName' },
  { label: '民族', field: 'nation' },
  { label: '工作单位', field: 'workUnit' },
  { label: '血型', field: 'bloodTypeName' },
  { label: '文化程度', field: 'education' },
  { label: '婚姻状况', field: 'maritalStatus' },
  { label: '职业', field: 'occupationCategory' },
  { label: '医疗费用支付方式', field: 'metPayFlag' },
  { label: '药物过敏史', field: 'encodingAllergenName' },
  { label: '暴露史', field: 'exposureHistoryName' },

  { label: '既往史', field: '', gap: true },

  { label: '疾病史(含外伤)', field: 'diseaseName', multi: true },
  { label: '手术', field: 'operationName', multi: true },
  { label: '输血', field: 'bloodTransfusionName', multi: true },

  { label: '家族史', field: '', gap: true },

  { label: '父亲', field: 'fatherName' },
  { label: '母亲', field: 'motherName' },
  { label: '兄弟姐妹', field: 'brothersSistersName' },
  { label: '子女', field: 'childrenName' },
  { label: '遗传病史', field: 'geneticHistory', multi: true },
  { label: '残疾情况', field: 'disability', multi: true },

  { label: '生活环境', field: '', gap: true },

  { label: '厨房排风设施', field: 'kitchenVentilation', multi: true },
  { label: '燃料类型', field: 'fuelType', multi: true },
  { label: '饮水', field: 'familyWater', multi: true },
  { label: '厕所', field: 'familyBathroom', multi: true },
  { label: '禽畜栏', field: 'familyCorralType', multi: true },
]

function ResidentHealthInfo() {
  const { cacheKey } = useContext(Context)
  const { data: residentHealthInfo } = useRequest(getResidentHealthInfo, {
    cacheKey: `${cacheKey}-residentHealthInfo`,
    staleTime: -1
  })
  return <>
    <StyledCellList data={residentHealthInfo} fields={residentHealthFields} />
    <View className='gap-line'></View>
    <StyledCellList data={residentHealthInfo} fields={residentHealthFields2} />
  </>
}

const sexMap = {
  1: '男',
  2: '女'
}

function BasicInfo() {
  const { cacheKey } = useContext(Context)
  const { data: patientInfo } = useRequest(getPatientInfo, {
    cacheKey: `${cacheKey}-patientInfo`,
    staleTime: -1,
    onSuccess(res) {
      res.sexName = sexMap[res.sex]
      res.bloodTypeName = res.abo || res.rhBlood
      res.permanentAddressFlagName = res.permanentAddressFlag === '1' ? '常住' : '流动'
    }
  })
  return <StyledCellList data={patientInfo} fields={patientInfoField} />
}

const tabs = [
  { text: '居民健康档案', key: 'residentHealthInfo', comp: ResidentHealthInfo },
  { text: '个人基本信息', key: 'basicInfo', comp: BasicInfo },
]

export default function PersonalRecord() {
  const user = useUser()
  const [value, setValue] = useState<Tab>(tabs[0])
  const Comp = value.comp
  return <View className='content-wrapper'>
    <View className='content'>
      <Flex className='personal-record-header header' justify='space-between'>
        <StyledSpace>
          <View>编号</View>
          <View>{user.phrId}</View>
        </StyledSpace>
        <Image src={logoUrl} className='logo' />
      </Flex>
      <TabSelect tabs={tabs} value={value} onChange={setValue} />
      <Comp />
    </View>
  </View>
}
