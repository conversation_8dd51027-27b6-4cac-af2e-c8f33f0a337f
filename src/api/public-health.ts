import { getRequest } from './request'

export type DiabetesFollowerUpItem = {
  label: string
  value: string
}
export type FollowUpList = {
  visitId: string
  visitDate: string
  visitDoctor: string
  symptoms: string
  visitWay: string
  nextDate: string
}
export type FollowUpInfo = {
  patientName: string
  followUpTime: string
  followUpClass: string
  symptom: string
  followUpNo: string
  bp: any
  heartRate: string
  weight: string
  otherSigns: any
  smokeCount: number
  drinkCount: number
  targetSalt: string
  psychologyChange: string
  sport: string
  obeyDoctor: string
  insulinTypeOne: any
  frequencyOne: any
  doseOne: any
  insulinTypeTwo: any
  frequencyTwo: any
  doseTwo: any
  insulinTypeThree: any
  frequencyThree: any
  doseThree: any
  insulinTypeFour: any
  frequencyFour: any
  doseFour: any
  medicine: any
  auxiliaryExamination: any
  adverseReactions: string
  nextDate: string
  visitType: string
  visitDoctor: string
  bmi: any
  otherCheck?: string
  fbs?: string
  pulsation?: string
  food: string
  trainTimesWeek?: string
  trainMinute?: string
}
export type TabProps = {
  type: string
  tabKey: string
  text?: string
  key?: string
}

/** 糖尿病管理基本信息 */
export const getDiabetesMgBasicInfo = (empiId) => getRequest<DiabetesFollowerUpItem[]>('diabetesManager/getBasicInfo/' + empiId)

/** 高血压管理基本信息 */
export const getHypertensionMgBasicInfo = (empiId) => getRequest<DiabetesFollowerUpItem[]>('hypertensionmanage/getBasicInfo/' + empiId)

/** 高血压管理随访情况 */
export const getFollowUpList = (empiId) => getRequest<FollowUpList[]>('hypertensionmanage/getFollowUpList/' + empiId)
/** 糖尿病管理随访情况 */
export const getDiabetesFollowerUp = (empiId) => getRequest<FollowUpList[]>('diabetesManager/getDiabetesFollowerUp/' + empiId)

/** 糖尿病管理服药情况 */
export const getDiabetesMedication = (empiId) => getRequest<FollowUpList[]>('diabetesManager/getDiabetesMedication/' + empiId)
/** 高血压服药情况 */
export const getUsedrug = (empiId) => getRequest<FollowUpList[]>('hypertensionmanage/getUsedrug/' + empiId)

// 糖尿病管理随访情况详情
export const getFollowerUpDetailById = (empiId) => getRequest<FollowUpInfo>('diabetesManager/getFollowerUpDetailById/' + empiId)

// 高血压管理随访情况详情
export const getFollowUpInfo = (empiId) => getRequest<FollowUpInfo>('hypertensionmanage/getFollowUpInfo/' + empiId)
