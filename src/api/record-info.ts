import { getRequest } from './request'

export type IResidentHealthInfo = {
  phrId: string
  name: string
  presentAddressProvince: string
  presentAddressCity: string
  presentAddressCounty: string
  presentAddressTown: string
  presentAddressVillage: string
  homeAddressProvince: string
  homeAddressCity: string
  homeAddressCounty: string
  homeAddressTown: string
  homeAddressVillage: string
  homeAddressHouse: any
  presentAddressHouse: any
  mobileNumber: string
  filingUnit: string
  creatorName: string
  doctorName: string
  creatorTime: string
  endTime: any
  endReason: any
  empiId: string
  jtdabh: string
  homeAddress: string
  presentAddress: string
}

export const getResidentHealthInfo = getRequest<IResidentHealthInfo>('recordInfo/getResidentHealthInfo')
export const getPatientInfo = getRequest<any>('recordInfo/getPatientInfo')
export const getFamilyRecordInfo = getRequest('recordInfo/getFamilyRecordInfo')
