import { getRequest } from './request'

type IHealthEducation = {
  id: string
  educationTitle: string
  isOriginal: number
  originalUrl: any
  editWay: number
  jumpUrl: string
  mainContent: string
  displayPriority: any
  status: number
  delFlag: number
  createTime: string
  createUser: string
  updateTime: string
  updateUser: string
  orgName: string
  deptName: string
  cacheId: string
  dmlType: string
  versionId: string
}

export const getHealthEducationList = getRequest<IHealthEducation[]>('healthEducation/getHealthEducationList')
export const getHealthEducationDetail = getRequest<IHealthEducation>('healthEducation/getHealthEducationDetail')
