import { getTokenOut } from '@/store'
import { postRequest, baseUrl } from './request'

export type LifeCycleInfo = {
  value: number
  name: string
  beginYear: string
  endYear: string
  desc: string
}

export const getLifeCycleInfo = postRequest<LifeCycleInfo[]>('homePage/getLifeCycleInfo')
export const getCheckList = postRequest<any[]>('physicalExamVisitInfo/getList')
export const getCheckDetail = (pkPhysicalExamReport: string) => `${baseUrl}/physicalExamVisitInfo/getReport?pkPhysicalExamReport=${pkPhysicalExamReport}&userToken=${getTokenOut()}`
