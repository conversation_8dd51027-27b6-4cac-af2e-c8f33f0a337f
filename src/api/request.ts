import { request, showToast } from '@tarojs/taro'
import { getTokenOut, getLogoutOut } from '@/store'

export const baseUrl = '/api'
type ApiRequest<T> = (...args: any) => Promise<T>

type RequestOption = Partial<{
  noHandler: boolean
}> & Partial<Taro.request.Option>

const createRequest = (options: RequestOption) => async (params) => {
  const { dataType, url, method, noHandler, header } = options
  const normalizedUrl = url?.replace(/^\//, '')
  return request({
    url: `${baseUrl}/${normalizedUrl}`,
    data: params,
    dataType,
    header: {
      'user_token': getTokenOut(),
      ...header
    },
    timeout: 20000,
    method
  })
    .then((res) => {
      if (res.statusCode === 401) {
        if (url === 'base/login') {
          throw new Error('登录失败')
        } else {
          throw new Error('login')
        }
      }
      if (res.statusCode !== 200) {
        throw new Error()
      }
      if (res.header['content-type']?.includes('application/json')) {
        const data = res.data.data
        const code = res.data.code
        if (code === 401) {
          if (url === 'base/login') {
            throw new Error('登录失败')
          } else {
            throw new Error('login')
          }
        }
        if (code !== 1) {
          console.log(47, res.data.errorMsg, res.data)
          throw new Error(res.data.errorMsg || res.data.message || res.data.msg)
        }
        if (!noHandler && data && typeof data === 'object' && 'pageSize' in data) return data.list
        return data !== undefined ? data : res.data
      }
      return res.data
    })
    .catch((e) => {
      console.log('error:', e)
      if (e.name === 'AbortError') return
      if (e.message === 'login') {
        getLogoutOut()()
        return Promise.reject(e)
      }
      showToast({
        title: e.message || '服务异常',
        icon: 'error'
      })
      return Promise.reject(e)
    })
}

export const getRequest = <T,>(url: string, option?: RequestOption): ApiRequest<T> => createRequest({
  url,
  method: 'GET',
  ...option
})
export const postRequest = <T,>(url: string, option?: RequestOption): ApiRequest<T> => createRequest({
  url,
  method: 'POST',
  ...option
})
export const deleteRequest = <T,>(url: string, option?: RequestOption): ApiRequest<T> => createRequest({
  url,
  method: 'DELETE',
  ...option
})
