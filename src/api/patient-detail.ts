import { getTokenOut } from '@/store'
import { baseUrl, getRequest, postRequest } from "./request"

export type Patient = {
  pkPatientInfo: string
  pkVisitInfo: string
  empiId: string
  patientName: string
  patientSexCode: string
  patientSex: string
  nurserLevel: any
  bedNo: string
  patientAge: string
  patientId: string
  deptName: string
  doctor: string
  diagnosticName: string
  operationName: string
  idCardNo: string

  type: string
}

export type ClinicMedicalRecord = {
  pkVisitInfo: string;
  visitDate: string;
  hospitalOrg: string;
  diagnosisDoctor: string;
  diagnosisDept: string;
  diagnoseDiseaseName: string;
};

export type InpatientMedicalRecord = {
  deptName: string;
  diagnosisInfo: string;
  hospitalOrg: string;
  pkInpatientVisitInfo: string;
  admitHospitalTime: string;
  leaveTime: string;
  admitHospitalRecord: string;
  admitHospitalRecordUser: string;
  admitHospitalRecordTime: string;
  progressRecord: string;
  leaveHospitalRecord: string;
};

export type ElectronicCaseHistoryDoc = {
  pkElectronicCaseHistoryDoc: string;
  docCreateTime: string;
  docCreatorName: string;
  docName: string;
};
export interface DiagnosisInfo {
  id: string;
  pkPatientInfo: string;
  outpatientNo: string;
  inpatientNo: string;
  patientId: string;
  visitTypeCode: string;
  visitType: string;
  visitTimes: string;
  visitDate: string;
  hospitalOrgCode: string;
  hospitalOrg: string;
  diagnosisDoctor: string;
  diagnosisDept: string;
  diagnoseDiseaseName: any;
  admitHospitalTime: string;
  leaveTime: string;
  title: string;
}
export interface ExamineResultReport {
  pkExamineReport: string;
  examineReportDate: string;
  examineReportItem: string;
  reptResultObjectiveOpinion: string;
  reptResultSubjectiveOpinion: string;
  examineReportDept: string;
  examineReportDoctor: string;
  pkVisitInfo: string;
  examineReportOrg: string;
}
export interface PateintTestreport {
  labReportNo: string;
  labReportItem: string;
  hospitalOrg: string;
  labReportDate: string;
  labReportDept: string;
  labReportDoctor: string;
  testResultDetailList: [
    {
      pkLabTestResultDetail: string;
      labTestIndexesDesc: string;
      labTestIndexes: string;
      labQuantitativeResult: string;
      qttativeResultMeasureUnit: string;
      labResultCode: string;
      referenceValueRange: string;
      criticalValueFlag: string;
    }
  ];
}

export interface OutpatientPrescriptionItem {
  visitDate: string;
  hospitalOrgCode: string;
  hospitalOrg: string;
  diagnosisDoctor: string;
  diagnosisDept: string;
  diagnoseDiseaseName: string;
  pkVisitInfo: string;
  prescriptionType: string;
  medicationVOS: [
    {
      tcmUsageMethod: any;
      dosage?: any;
      yl?: string;
      drugUseFrequency?: string;
      prescriptionEffectiveDays?: any;
      drugType: string;
      medicOrdersItem?: string;
      drugSpecification?: string;
      drugDosageUnit?: string;
      medicationVOS?: [
        {
          medicOrdersItem: string;
          drugDosageUnit: string;
        }
      ];
    }
  ];
}
export interface InPateintMedicalOrders {
  deptName: string;
  diagnosisInfo: string;
  hospitalOrg: string;
  admitHospitalTime: string;
  visitSerialNo: string;
  medicOrdersdoctor: string;
  medicalOrdersVOList: [
    {
      medicOrdersType: string;
      medicOrdersTypeCode: string;
      medicOrdersItemCode: string;
      medicOrdersItem: string;
      medicOrdersItemType: string;
      medicOrdersStopDate: string;
      medicOrdersExcuteDate: string;
      drugName: any;
      drugUsed: any;
      drugSpec: any;
      drugUsage: any;
      drugUseDrequency: any;
    }
  ];
}

/** 门诊病历 */
export const getClinicMedicalRecordList = getRequest<ClinicMedicalRecord[]>(
  "clinicalVisitInfo/getClinicMedicalRecordList"
);
/** 住院病历 */
export const getInpatientMedicalRecordList = postRequest<
  InpatientMedicalRecord[]
>("inpatientVisitInfo/inPateintMedicalPage");
/** 病历列表 */
export const getElectronicCaseHistoryDocList = postRequest<
  ElectronicCaseHistoryDoc[]
>("electronicCaseHistoryDoc/queryList");
/** 病历详情 */
export const getElectronicCaseHistoryDocDetailUrl = (
  pkElectronicCaseHistoryDoc: string
) =>
  `${baseUrl}/electronicCaseHistoryDoc/detail?pkElectronicCaseHistoryDoc=${pkElectronicCaseHistoryDoc}&userToken=${getTokenOut()}`;

/** 就诊记录 */
export const getDiagnosisInfoList = postRequest<DiagnosisInfo[]>(
  "diagnosisInfo/queryList"
);
/** 检查报告 */
export const getExamineResultReportList = getRequest<ExamineResultReport[]>(
  "/examineResultReport/getExamineResultReportList"
);
//
/** 检验报告 */
export const getPateintTestreport = postRequest<PateintTestreport[]>(
  "/labTestResultReport/pateintTestreport"
);
/** 门诊处方 */
export const getOutpatientPrescription = postRequest<
  OutpatientPrescriptionItem[]
>("/clinicalVisitInfo/outpatientPrescription");

/** 住院医嘱 */
export const getInPateintMedicalOrders = postRequest<InPateintMedicalOrders[]>(
  "/inpatientVisitInfo/inPateintMedicalOrders"
);
