import { postRequest, getRequest } from './request'

export type UserInfo = {
  id: string
  empiId: string
  name: string
  age: any
  sex: string
  phrId: string
  mobileNumber: string
  idCard: string
  jtdabh: string
}

export type PersonInfo = {
  pkMedicalEmployeeInfo: string
  employeeCode: string
  employeeName: string
  sexCode: string
  sex: string
  age: string
  deptName: string
  jobTitle: string
  jobName: string
  patientCollectionNum: number
  avatar: string
}

export const login = postRequest<string>('base/login')
export const getUserInfo = getRequest<UserInfo>('base/getUserInfo')
export const sendVerificationCode = postRequest('base/sendVerificationCode')
export const getPastHistory = postRequest('pastHistory/getDetail')



interface IHealthCardInfo  {
  healthCardId: string
  name: string
  phone1: string
  phone2: string
  idNumber: string
}

/**
 * 获取健康卡信息
 * @param params
 * @param params.healthCode 微信选择健康卡跳转回来获取的healthCode
 * @param params.mpCode 微信网页授权获取的code
 * /app/healthCard/getHealthCardInfo
 */
export const getHealthCardInfo= postRequest<IHealthCardInfo>('app/healthCard/getHealthCardInfo')

/**
 * 通过健康卡信息登录
 * @param params
 * @param params.username 健康卡姓名
 * @param params.telephone 健康卡手机号
 * @param params.idCard 健康卡身份证号
 * /healthCard/login
 */

export const healthCardLogin = postRequest<string>('app/healthCard/login')
