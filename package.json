{"name": "rha-h5", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"dev": "yarn dev:h5", "build": "yarn build:h5", "build:test": "cross-env vConsole=true yarn build", "build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "test": "jest", "lint": "eslint --ext .js,.jsx,.ts,.tsx --fix src --cache --cache-location node_modules/.cache/eslint --max-warnings 0", "lint:prettier": "prettier --write  \"{src,config}/**/*.{js,json,tsx,css,less,scss,vue,html,md}\""}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@taroify/core": "0.1.0-alpha.9", "@tarojs/components": "3.6.13", "@tarojs/helper": "3.6.13", "@tarojs/plugin-framework-react": "3.6.13", "@tarojs/plugin-platform-alipay": "3.6.13", "@tarojs/plugin-platform-h5": "3.6.13", "@tarojs/plugin-platform-jd": "3.6.13", "@tarojs/plugin-platform-qq": "3.6.13", "@tarojs/plugin-platform-swan": "3.6.13", "@tarojs/plugin-platform-tt": "3.6.13", "@tarojs/plugin-platform-weapp": "3.6.13", "@tarojs/react": "3.6.13", "@tarojs/runtime": "3.6.13", "@tarojs/shared": "3.6.13", "@tarojs/taro": "3.6.13", "ahooks": "^3.7.8", "classnames": "^2.3.2", "dayjs": "^1.11.9", "lodash-es": "^4.17.21", "pdfjs-dist": "^3.10.111", "react": "^18.0.0", "react-dom": "^18.0.0", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.13", "@tarojs/taro-loader": "3.6.13", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "3.6.13", "@types/jest": "^29.3.1", "@types/lodash-es": "^4.17.8", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "3.6.13", "cross-env": "^7.0.3", "eslint": "^8.12.0", "eslint-config-taro": "3.6.13", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "lint-staged": "^14.0.1", "postcss": "^8.4.18", "prettier": "^3.0.3", "react-refresh": "^0.11.0", "simple-git-hooks": "^2.9.0", "stylelint": "^14.4.0", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "^5.1.0", "webpack": "5.78.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist|npm)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.{scss,less,styl,html}": ["prettier --write"], "*.md": ["prettier --write"]}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}}